'use client';

import { useEffect } from 'react';
import { useRouteMemory } from '@/app/hooks/useRouteMemory';

/**
 * Component that silently tracks route changes and updates the backend
 * This component doesn't render anything visible
 */
export const RouteTracker = () => {
    // Initialize the route memory hook
    const routeMemory = useRouteMemory();

    // Add cleanup on component unmount
    useEffect(() => {
        return () => {
            // Any cleanup needed when the component unmounts
        };
    }, []);

    // This component doesn't render anything
    return null;
};

export default RouteTracker;
