import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Auth<PERSON>ontroller } from './auth.controller';
import { RouteController } from './route.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategy/jwt-auth.strategy';
import { UsersModule } from '../users/users.module';
import { RolesGuard } from './guards/roles.guard';
import { RoleModule } from '../roles/role.module';
import { UserOtpModule } from '../user-otps/user-otps.module';
import { User } from '../users/entities/user.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SESModule } from '../utils/aws/ses/ses.module';
import { SessionModule } from '../session/session.module';

@Module({
	imports: [
		TypeOrmModule.forFeature([User]),
		UsersModule,
		PassportModule,
		RoleModule,
		UserOtpModule,
		JwtModule.registerAsync({
			imports: [ConfigModule],
			useFactory: async (configService: ConfigService) => ({
				secret: configService.get<string>('JWT_SECRET'),
				signOptions: { expiresIn: '1d' }
			}),
			inject: [ConfigService]
		}),
		ConfigModule,
		UserOtpModule,
		SESModule,
		SessionModule
	],
	controllers: [AuthController, RouteController],
	providers: [AuthService, JwtStrategy, RolesGuard],
	exports: [AuthService]
})
export class AuthModule {}
