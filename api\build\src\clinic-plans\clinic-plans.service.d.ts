import { ClinicPlan } from './entities/clinic-plan.entity';
import { Repository } from 'typeorm';
import { ClinicProductsService } from '../clinic-products/clinic-products.service';
import { ClinicVaccinationsService } from '../clinic-vaccinations/clinic-vaccinations.service';
import { ClinicServicesService } from '../clinic-services/clinic-services.service';
import { EnumPlanType } from './enums/enum-plan-type';
import { ClinicLabReportService } from '../clinic-lab-report/clinic-lab-report.service';
import { ClinicMedicationsService } from '../clinic-medications/clinic-medications.service';
export declare class ClinicPlansService {
    private readonly clinicPlansRepository;
    private readonly clinicProductsservice;
    private readonly clinicVaccinationsservice;
    private readonly clinicServicesservice;
    private readonly clinicMedicationService;
    private readonly clinicLabReportsService;
    constructor(clinicPlansRepository: Repository<ClinicPlan>, clinicProductsservice: ClinicProductsService, clinicVaccinationsservice: ClinicVaccinationsService, clinicServicesservice: ClinicServicesService, clinicMedicationService: ClinicMedicationsService, clinicLabReportsService: ClinicLabReportService);
    findAll(clinicId: string, searchKeyword?: string, excludeTypes?: EnumPlanType[]): Promise<any[]>;
}
