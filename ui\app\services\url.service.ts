import { AppointmentParams } from '../types/appointment';
import { GetPatientsT } from '../types/patient';
import { DoctorAvailabilityParams, GetDoctorsType } from '../types/provider';

const ApiUrl = process.env.NEXT_PUBLIC_API_URL;
type Params = { [key: string]: string | number };

const UrlParamsReplace = (
    url: string,
    pathParams: Params = {},
    queryParams: Params = {}
) => {
    let urlWithPrefix = `${ApiUrl}${url}`;

    // Replace path parameters
    if (pathParams) {
        Object.keys(pathParams).forEach(
            (key) =>
                (urlWithPrefix = urlWithPrefix.replace(
                    `:${key}`,
                    String(pathParams[key])
                ))
        );
    }

    // Add query parameters
    if (Object.keys(queryParams).length > 0) {
        const queryString = Object.entries(queryParams)
            .map(
                ([key, value]) =>
                    `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
            )
            .join('&');
        urlWithPrefix += `?${queryString}`;
    }

    return urlWithPrefix;
};

export const GET_CLINIC_PATIENTS = (
    page: number,
    limit: number,
    searchTerm: string,
    withBalance: string,
    patientStatus: string = 'all'
) =>
    UrlParamsReplace(
        '/patients',
        {},
        { page, limit, searchTerm, withBalance, patientStatus }
    );

export const GET_PATIENTS = ({ page, limit, clinicId, search }: GetPatientsT) =>
    UrlParamsReplace(
        '/patients/clinic/:clinicId',
        { clinicId },
        { page, limit, search }
    );

export const GET_PATIENT_DETAILS = (id: any) =>
    UrlParamsReplace('/patients/:id', { id }, {});

//appointments
export const CREATE_APPOINTMENT = () => UrlParamsReplace('/appointments');
export const GET_APPOINTMENTS = ({
    page,
    limit,
    orderBy,
    date,
    search,
    doctors,
    status,
    onlyPrimary,
}: AppointmentParams) =>
    UrlParamsReplace(
        '/appointments',
        {},
        {
            page,
            limit,
            orderBy,
            date,
            search,
            doctors: JSON.stringify(doctors),
            status: JSON.stringify(status),
            onlyPrimary: JSON.stringify(onlyPrimary),
        }
    );

export const SEARCH_PATIENT_BY_PHONE = (phoneNumber: string) =>
    UrlParamsReplace('/owners/search', {}, { phoneNumber });

export const CREATE_PATIENT = () => UrlParamsReplace('/patients');

export const UPDATE_PATIENT = (patientId: string) =>
    UrlParamsReplace('/patients/:id', { id: patientId });
//patient-alert
export const CREATE_PATIENT_ALERT = UrlParamsReplace('/patient-alert', {}, {});
export const DELETE_PATIENT_ALERT = (id: any, all?: string) =>
    UrlParamsReplace('/patient-alert/:id', { id }, { ...(all ? { all } : {}) });
export const GET_PATIENT_ALERT = (patientId: string) =>
    UrlParamsReplace('/patient-alert/:patientId', { patientId }, {});
export const UPDATE_PATIENT_ALERT = (id: string) =>
    UrlParamsReplace('/patient-alert/:id', { id }, {});

export const GET_CLINIC_DOCTORS = ({
    page,
    limit,
    role,
    clinicId,
    orderBy = 'ASC',
}: GetDoctorsType) => {
    return UrlParamsReplace(
        '/users/clinic/:clinicId',
        { clinicId },
        { page, limit, orderBy, ...(role && { role }) }
    );
};
export const GET_CLINIC_DOCTORS_AVAILABILITY = ({
    clinicId,
    date,
    startTime,
    endTime,
    role,
    orderBy = 'ASC',
}: DoctorAvailabilityParams) => {
    return UrlParamsReplace(
        '/users/clinic/availability/:clinicId',
        { clinicId },
        {
            orderBy,
            ...(date && { date }),
            ...(startTime && { startTime }),
            ...(endTime && { endTime }),
            ...(role && { role }),
        }
    );
};
// Update appointment details (SOAP)
export const UPDATE_APPOINTMENT_DETAILS = (appointmentId: string) => {
    return UrlParamsReplace('/appointments/:id/details', { id: appointmentId });
};

//clinics rooms
export const GET_CLINIC_ROOMS = (clinicId: string) => {
    return UrlParamsReplace('/clinics/:id/rooms', { id: clinicId });
};

export const CREATE_CLINIC_ROOM = () => UrlParamsReplace('/clinics/rooms');

export const UPDATE_CLINIC_ROOM = (id: string) =>
    UrlParamsReplace(`/clinics/rooms/${id}`);

export const DELETE_ROOM = (roomId: string) =>
    UrlParamsReplace(`/clinics/rooms/${roomId}`);

export const UPDATE_CLINIC_DETAILS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId', { clinicId });

export const CREATE_CLINIC_USER = (clinicId: string, brandId: string) =>
    UrlParamsReplace(`/users?clinicId=${clinicId}&brandId=${brandId}`);

export const GET_CLINIC_WORKING_HOURS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId/working-hours', { clinicId });

export const UPDATE_CLINIC_WORKING_HOURS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId/working-hours', { clinicId });

export const GET_USERS = (clinicId: string, page: number, limit: number) =>
    UrlParamsReplace('/users/clinic/:clinicId', { clinicId }, { page, limit });

export const UPDATE_USER_STATUS = (userId: string) =>
    UrlParamsReplace('/users/:userId/status', { userId });

export const GET_USER_DETAILS = (userId: string) =>
    UrlParamsReplace('/users/:userId', { userId });

export const UPDATE_USER_DETAILS = (userId: string) =>
    UrlParamsReplace('/users/clinic/:userId', { userId });

export const COMPLETE_STAFF_DETAILS = (globalUserId: string) =>
    UrlParamsReplace('/users/complete-profile/:globalUserId', { globalUserId });

export const GET_ALL_ROLES = () => UrlParamsReplace('/roles');

export const SEARCH_USERS_ACROSS_CLINICS = (
    brandId: string,
    searchTerm: string,
    excludeClinicId: string
) =>
    UrlParamsReplace(
        '/users/search',
        {},
        { brandId, searchTerm, excludeClinicId }
    );

export const ADD_USER_TO_CLINIC = (
    userId: string,
    clinicId: string,
    brandId: string
) =>
    UrlParamsReplace('/users/:userId/add-to-clinic/:clinicId', {
        userId,
        clinicId,
        brandId,
    });

// Appointment API
export const GET_LATEST_PATIENT_APPOINTMENTS = (patientId: string) =>
    UrlParamsReplace('/appointments/patients/:patientId', { patientId });

export const GET_APPOINTMENT_DETAILS = (appointmentId: string) =>
    UrlParamsReplace('/appointments/:appointmentId', { appointmentId });

// Update appointment
export const UPDATE_APPOINTMENT_STATUS = (appointmentId: string) =>
    UrlParamsReplace('/appointments/:appointmentId/status', { appointmentId });

export const CHECK_PATIENT_ONGOING_APPOINTMENT = (patientId: string) =>
    UrlParamsReplace(
        '/appointments/patients/:patientId/check-ongoing-appointment',
        { patientId }
    );
// Pin Login
export const LOGIN_WITH_PIN = () => UrlParamsReplace('/auth/login/pin', {});

export const RESET_PIN_URL = UrlParamsReplace('/auth/reset-pin');

export const VERIFY_PIN_URL = () => UrlParamsReplace('/auth/verify-pin', {});
// Email, Otp Login
type LoginWithEmailOtpParams = {
    email: string;
    otp: string;
};
export const GENERATE_OTP_FOR_EMAIL = () => UrlParamsReplace('/otp/generate');

export const LOGIN_WITH_EMAIL_OTP = () =>
    UrlParamsReplace('/auth/login/users/email');

// Get lab reports
export const GET_LAB_REPORTS = (search: string, clinicId: string) =>
    UrlParamsReplace('/clinic-lab-reports/types', { search }, { clinicId });

// Get assessment list
export const GET_ASSESSMENT_LIST = (search: string) => {
    return UrlParamsReplace('/appointment-assessment', { search });
};

// Post new assessment
export const CREATE_NEW_ASSESSMENT = () => {
    return UrlParamsReplace('/appointment-assessment');
};

// Get plan list
export const GET_PLAN_LIST = (
    search: string,
    exclude: string,
    clinicId: string
) => {
    const queryParams = new URLSearchParams({
        search: search,
        exclude: exclude,
        clinicId: clinicId,
    });

    return UrlParamsReplace(`/clinic-plans?${queryParams.toString()}`);
};

// Get medication list
export const GET_PRESCRIPTION_LIST = (
    search: string,
    clinicId: string,
    all: boolean = false
) => {
    return UrlParamsReplace(
        '/clinic-medications',
        {},
        { clinicId, all: all ? 'true' : 'false' }
    );
};

export const GET_DIAGNOSTIC_LIST = (search: string, clinicId: string) => {
    return UrlParamsReplace(
        '/clinic-lab-reports/types',
        { search },
        { clinicId }
    );
};
export const UPDATE_APPOINTMENT_FEILDS = (appointmentId: string) => {
    return UrlParamsReplace('/appointments/:appointmentId', { appointmentId });
};

export const DELETE_APPOINTMENT = (appointmentId: string) => {
    return UrlParamsReplace('/appointments/:appointmentId', { appointmentId });
};

export const GET_UPLOAD_PRE_SIGED_URL = () =>
    UrlParamsReplace('/aws-s3/signed-url');

export const CREATE_LAB_REPORT = (isCreate?: boolean) =>
    UrlParamsReplace(
        '/clinic-lab-reports/lab-report',
        {},
        isCreate !== undefined ? { isCreate: isCreate.toString() } : {}
    );

export const UPDATE_LAB_REPORT_STATUS = () =>
    UrlParamsReplace('/clinic-lab-reports/status');

export const GET_VIEW_PRE_SIGNED_URL = (fileKey: string) =>
    UrlParamsReplace(
        `/aws-s3/view-signed-url?fileKey=${encodeURIComponent(fileKey)}`
    );

export const DELETE_LAB_REPORT_FILE = (
    labReportId: string,
    fileKey: string,
    lineItemId: string
) =>
    UrlParamsReplace(
        `/clinic-lab-reports/${labReportId}/files/${encodeURIComponent(fileKey)}?lineItemId=${lineItemId}`
    );

export const DELETE_LAB_REPORT = (
    labReportId: string,
    appointmentId: string,
    lineItemId: string
) =>
    UrlParamsReplace(
        `/clinic-lab-reports/lab-report/${labReportId}?appointmentId=${appointmentId}&lineItemId=${lineItemId}`
    );

// export const GET_ALL_LAB_REPORTS = () => UrlParamsReplace('/clinic-lab-reports');

export const GET_DOWNLOAD_PRESIGNED_URL = (
    fileKey: string,
    fileType: 'img' | 'pdf' | '' = ''
) =>
    UrlParamsReplace(
        `/aws-s3/download-file?fileKey=${encodeURIComponent(fileKey)}&fileType=${fileType}`
    );

export const GET_ALL_LAB_REPORTS = (params: {
    clinicId: string;
    page?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
    searchTerm?: string;
    status?: string;
}) => {
    const queryParams = new URLSearchParams({
        clinicId: params.clinicId,
        page: (params.page || 1).toString(),
        limit: (params.limit || 10).toString(),
        searchTerm: params.searchTerm || '',
        status: params.status || '',
    });

    if (params.startDate) {
        queryParams.append('startDate', params.startDate);
    }

    if (params.endDate) {
        queryParams.append('endDate', params.endDate);
    }

    return UrlParamsReplace(`/clinic-lab-reports?${queryParams.toString()}`);
};

export const GET_LAB_REPORTS_FOR_PATIENT = (patientId: string) =>
    UrlParamsReplace('/clinic-lab-reports/diagnostic/:patientId', {
        patientId,
    });

export const GET_LAB_REPORT_BY_ID = (labReportId: string) =>
    UrlParamsReplace('/clinic-lab-reports/lab-report/:id', {
        id: labReportId,
    });

// Post new medication/prescription
export const CREATE_NEW_PRESCRIPTION = () => {
    return UrlParamsReplace('/clinic-medications');
};

// Add long term prescription data
export const ADD_LONG_TERM_PRESCRIPTION = () => {
    return UrlParamsReplace('/long-term-medications');
};

// Delete long term prescription data
export const DELETE_LONG_TERM_PRESCRIPTION = (
    patientId: string,
    medicationId: string
) => {
    return UrlParamsReplace(
        '/long-term-medications',
        {},
        {
            patientId,
            medicationId,
        }
    );
};

// Get long term prescription data
export const GET_LONG_TERM_PRESCRIPTION = (patientId: string) => {
    return UrlParamsReplace(
        '/long-term-medications',
        {},
        {
            patientId,
        }
    );
};

// tasks
export const CREATE_TASK = UrlParamsReplace(`/tasks`);

export const GET_TASK = (userId: string) => {
    return UrlParamsReplace('/tasks/:userId', { userId });
};

export const DELETE_TASK = (id: string) => {
    return UrlParamsReplace('/tasks/:id', { id });
};

export const UPDATE_TASK = (id: string) => {
    return UrlParamsReplace('/tasks/:id', { id });
};

//users

export const GET_ALL_USER = UrlParamsReplace('/users');

//chats
export const GET_USER_CHAT_ROOMS = (userId: string) => {
    return UrlParamsReplace('/chat-rooms/user/:userId', { userId });
};

export const GET_CHAT_ROOM_DETAILS = (chatRoomId: string) => {
    return UrlParamsReplace('/chat-rooms/:id', { id: chatRoomId });
};

export const CREATE_CHAT_ROOM = UrlParamsReplace('/chat-rooms');
export const SEND_MESSAGE = UrlParamsReplace('/chat-rooms/message');
export const UPDATE_CHAT_ROOM = (id: string) =>
    UrlParamsReplace('/chat-rooms/:id', { id });

// Brands
export const CREATE_BRAND = (name: string) =>
    UrlParamsReplace('/brands', {}, { name });

export const GET_BRANDS = (
    page?: number,
    limit?: number,
    orderBy?: string
) => {
    const params: any = {};
    if (page !== undefined) params.page = page;
    if (limit !== undefined) params.limit = limit;
    if (orderBy !== undefined) params.orderBy = orderBy;

    return UrlParamsReplace('/brands', {}, params);
};

export const GET_BRAND = (id: string) =>
    UrlParamsReplace('/brands/:id', { id });
export const GET_BRAND_BY_SLUG = (slug: string) =>
    UrlParamsReplace('/brands/slug/:slug', { slug });
// clinicAlerts

export const GET_CLINIC_ALERTS = (clinicId: string) =>
    UrlParamsReplace('/clinic-alerts/:clinicId', { clinicId }, {});

export const CREATE_CLINIC_ALERTS = UrlParamsReplace('/clinic-alerts', {}, {});

export const DELETE_CLINIC_ALERTS = (id: string) =>
    UrlParamsReplace('/clinic-alerts/:id', { id }, {});

export const UPDATE_CLINIC_ALERTS = (id: string) =>
    UrlParamsReplace('/clinic-alerts/:id', { id }, {});
// Delete from S3
export const DELETE_FROM_S3 = (fileKey: string) => {
    return UrlParamsReplace('/aws-s3', {}, { fileKey });
};

//patient-vaccinations

export const CREATE_PATIENT_VACCINATION = UrlParamsReplace(
    '/patient-vaccinations',
    {},
    {}
);
export const GET_PATIENT_VACCINATION = (patientId: string) =>
    UrlParamsReplace('/patient-vaccinations/:patientId', { patientId }, {});
export const UPDATE_PATIENT_VACCINATION = (id: string) =>
    UrlParamsReplace('/patient-vaccinations/:id', { id }, {});
// Add to cart
export const ADD_TO_CART = () => UrlParamsReplace('/cart-items');

// Delete from cart-item
export const DELETE_FROM_CART = (id: string, source?: string) => {
    const baseUrl = UrlParamsReplace('/cart-items/:id', { id });
    return source ? `${baseUrl}?source=${source}` : baseUrl;
};

// Get cart list for an appointment
export const GET_CART_LIST_FOR_AN_APPOINTMENT = (appointmentId: string) => {
    return UrlParamsReplace(
        '/cart-items',
        {},
        {
            appointmentId,
        }
    );
};
export const GET_CART_LIST_BY_CART_ID = (cartId: string) => {
    return UrlParamsReplace(
        '/cart-items',
        {},
        {
            cartId,
        }
    );
};
// Delete entire cart
export const DELETE_CART = (id: string) => {
    return UrlParamsReplace('/carts/:id', { id });
};
// Update cart details
export const UPDATE_CART_DETAILS = (cartId: string, source?: string) => {
    const baseUrl = UrlParamsReplace('/cart-items/:id/details', { id: cartId });
    return source ? `${baseUrl}?source=${source}` : baseUrl;
};

// Create invoice
export const CREATE_INVOICE = () => {
    return UrlParamsReplace('/invoices');
};

// Update invoice
export const UPDATE_INVOICE = (invoiceId: string) => {
    return UrlParamsReplace('/invoices/:invoiceId', { invoiceId });
};

// Delete invoice
export const DELETE_INVOICE = (invoiceId: string) => {
    return UrlParamsReplace('/invoices/:invoiceId', { invoiceId });
};

// Write off invoice
export const WRITE_OFF_INVOICE = (invoiceId: string) => {
    return UrlParamsReplace('/invoices/:invoiceId/write-off', { invoiceId });
};

// Create invoice with payment and appointment completion (atomic operation)
export const CREATE_INVOICE_WITH_PAYMENT = () => {
    return UrlParamsReplace('/invoices/create-invoice-with-payment');
};

// Create payment details - Credit collect, Credit return, Invoice
export const CREATE_PAYMENT_DETAILS = () => {
    return UrlParamsReplace('/payment-details');
};

// Create bulk payment for multiple invoices
export const CREATE_BULK_PAYMENT_DETAILS = () => {
    return UrlParamsReplace('/payment-details/bulk');
};

export const UPLOAD_CLINIC_EXCEL = (clinicId: string, brandId: string) =>
    UrlParamsReplace(
        `/clinics/bulk-upload?clinicId=${clinicId}&brandId=${brandId}`
    );

// Get Endpoints
export const GET_CLINIC_CONSUMABLES = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => UrlParamsReplace('/clinic-consumables', {}, { clinicId, page, limit });

export const GET_CLINIC_PRODUCTS = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => UrlParamsReplace('/clinic-products', {}, { clinicId, page, limit });

export const GET_CLINIC_MEDICATIONS = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => UrlParamsReplace('/clinic-medications', {}, { clinicId, page, limit });

export const GET_CLINIC_VACCINATIONS = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => UrlParamsReplace('/clinic-vaccinations', {}, { clinicId, page, limit });

export const GET_CLINIC_SERVICES = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) => UrlParamsReplace('/clinic-services', {}, { clinicId, page, limit });

export const GET_CLINIC_DIAGNOSTICS = (
    clinicId: string,
    page: number = 1,
    limit: number = 10
) =>
    UrlParamsReplace(
        '/clinic-lab-reports/types',
        {},
        { clinicId, page, limit, integrationType: '' }
    );

// create Endpoints
export const CREATE_CLINIC_CONSUMABLES = () =>
    UrlParamsReplace('/clinic-consumables');

export const CREATE_CLINIC_PRODUCTS = () =>
    UrlParamsReplace('/clinic-products');

export const CREATE_CLINIC_MEDICATIONS = () =>
    UrlParamsReplace('/clinic-medications');

export const CREATE_CLINIC_VACCINATIONS = () =>
    UrlParamsReplace('/clinic-vaccinations');

export const CREATE_CLINIC_SERVICES = () =>
    UrlParamsReplace('/clinic-services');

export const CREATE_CLINIC_DIAGNOSTICS = () =>
    UrlParamsReplace('/clinic-lab-reports');

// Update Endpoints
export const UPDATE_CLINIC_CONSUMABLES = (id: string) =>
    UrlParamsReplace('/clinic-consumables/:id', { id });

export const UPDATE_CLINIC_PRODUCTS = (id: string) =>
    UrlParamsReplace('/clinic-products/:id', { id });

export const UPDATE_CLINIC_MEDICATIONS = (id: string) =>
    UrlParamsReplace('/clinic-medications/:id', { id });

export const UPDATE_CLINIC_VACCINATIONS = (id: string) =>
    UrlParamsReplace('/clinic-vaccinations/:id', { id });

export const UPDATE_CLINIC_SERVICES = (id: string) =>
    UrlParamsReplace('/clinic-services/:id', { id });

export const UPDATE_CLINIC_DIAGNOSTICS = (id: string) =>
    UrlParamsReplace('/clinic-lab-reports/:id', { id });

// Delete Endpoints
export const DELETE_CLINIC_CONSUMABLES = (id: string) =>
    UrlParamsReplace('/clinic-consumables/:id', { id });

export const DELETE_CLINIC_PRODUCTS = (id: string) =>
    UrlParamsReplace('/clinic-products/:id', { id });

export const DELETE_CLINIC_MEDICATIONS = (id: string) =>
    UrlParamsReplace('/clinic-medications/:id', { id });

export const DELETE_CLINIC_VACCINATIONS = (id: string) =>
    UrlParamsReplace('/clinic-vaccinations/:id', { id });

export const DELETE_CLINIC_SERVICES = (id: string) =>
    UrlParamsReplace('/clinic-services/:id', { id });

export const DELETE_CLINIC_DIAGNOSTICS = (id: string) =>
    UrlParamsReplace('/clinic-lab-reports/:id', { id });

export const DOWNLOAD_LATEST_INVENTORY = (clinicId: string) =>
    UrlParamsReplace('/clinics/inventory/download/:clinicId', { clinicId });

export const DELETE_INVENTORY_ITEM = (itemType: string, itemId: string) =>
    UrlParamsReplace(`/clinics/inventory/${itemType}/:itemId`, { itemId });
export const CREATE_CLINIC = () => UrlParamsReplace('/clinics');

export const GET_ALL_CLINICS = (
    page?: number,
    limit?: number,
    orderBy?: string
) => {
    const params: any = {};
    if (page !== undefined) params.page = page;
    if (limit !== undefined) params.limit = limit;
    if (orderBy !== undefined) params.orderBy = orderBy;

    return UrlParamsReplace('/clinics', {}, params);
};

export const GET_CLINIC = (id: string) => {
    return UrlParamsReplace('/clinics/:id', { id });
};
export const DEACTIVATE_CLINIC = (id: string) =>
    UrlParamsReplace('/clinics/:id/deactivate', { id });

export const REACTIVATE_CLINIC = (id: string) =>
    UrlParamsReplace('/clinics/:id/reactivate', { id });

export const SOFT_DELETE_CLINIC = (id: string) =>
    UrlParamsReplace('/clinics/:id', { id });

export const EDIT_CLINIC_DETAILS = (id: string) =>
    UrlParamsReplace('/clinics/basic/:id', { id });

// Get payment details list for a patient id
export const GET_PAYMENT_DETAILS_LIST_FOR_A_PATIENT = (patientId: string) => {
    return UrlParamsReplace('/payment-details/:patientId', { patientId });
};

// Get payment details list for a owner id
export const GET_PAYMENT_DETAILS_LIST_FOR_A_OWNER = (ownerId: string) => {
    return UrlParamsReplace('/payment-details/owner-receipts/:ownerId', {
        ownerId,
    });
};

// Get payment receipts list for a patient id
export const GET_PAYMENT_RECEIPTS_LIST_FOR_A_PATIENT = (patientId: string) => {
    return UrlParamsReplace('/payment-details/patient-receipts/:patientId', {
        patientId,
    });
};

// Get invoices list for a patient id
export const GET_INVOICES_LIST_FOR_A_PATIENT = (patientId: string) => {
    return UrlParamsReplace('/payment-details/patient-invoices/:patientId', {
        patientId,
    });
};

export const GET_CLINIC_USER_DATA = (userId: string) =>
    UrlParamsReplace('/users/clinic-user/:userId', { userId });

export const GET_ALL_CLINIC_DOCTORS = (clinicId: string) =>
    UrlParamsReplace('/users/clinic/doctors/:clinicId', { clinicId });

export const UPDATE_WORKING_HOURS = (userId: string) =>
    UrlParamsReplace('/users/working-hours/:userId', { userId }); // userId here is

export const GET_USER_CLINICS = (userId: string) =>
    UrlParamsReplace('/users/clinics/:userId', { userId });

export const GET_USER_EXCEPTIONS = (
    clinicUserId: string,
    includeHistory: boolean = false
) =>
    UrlParamsReplace(
        '/users/exceptions/:clinicUserId',
        { clinicUserId },
        { includeHistory: String(includeHistory) }
    );

export const GET_CALENDAR_WORKING_HOURS = (date: string, clinicId: string) =>
    UrlParamsReplace('/users/calendar-working-hours', {}, { date, clinicId });

export const GET_EXCEPTION_DETAILS = (id: string) =>
    UrlParamsReplace('/users/exceptions/detail/:id', { id });

export const CREATE_EXCEPTION = () => UrlParamsReplace('/users/exceptions');

export const UPDATE_EXCEPTION = (id: string) =>
    UrlParamsReplace('/users/exceptions/:id', { id });

export const DELETE_EXCEPTION = (id: string) =>
    UrlParamsReplace('/users/exceptions/:id', { id });

export const ADD_LONG_TERM_PRESCRIPTION_TO_CART = () =>
    UrlParamsReplace('/cart-items/bulkInsert');

export const DELETE_DIAGNOSTICS = (appointmentId: string) => {
    return UrlParamsReplace('/clinic-lab-reports/appointment/:appointmentId', {
        appointmentId,
    });
};

export const GET_CLINIC_DETAILS = (clinicId: string) => {
    return UrlParamsReplace('/clinics/:clinicId', { clinicId });
};

/*********************** IDEXX **********************/
// Create Idexx entry
export const CREATE_IDEXX_ENTRY = () => {
    return UrlParamsReplace('/clinic-integrations');
};

// Get idexx entries
export const GET_IDEXX_ENTRIES = (clinicId: string) => {
    return UrlParamsReplace('/clinic-integrations', {}, { clinicId });
};

// Delete idexx entry
export const DELETE_IDEXX_ENTRY = (clinicIdexxId: string) => {
    return UrlParamsReplace('/clinic-integrations/:clinicIdexxId', {
        clinicIdexxId,
    });
};

// Get IDEXX test list
export const GET_IDEXX_TESTS_LIST = (clinicId: string) => {
    return UrlParamsReplace('/clinic-integrations/:clinicId/testsList', {
        clinicId,
    });
};

// Add IDEXX test item into clinic_lab_reports table
export const ADD_IDEXX_TEST_ITEM_TO_LABREPORTS_LIST = (clinicId: string) => {
    return UrlParamsReplace('/clinic-integrations/:clinicId/create', {
        clinicId,
    });
};

// Get all the selected/added IDEXX test lists
export const GET_SELECTED_IDEXX_TESTS_LIST = (
    search: string,
    clinicId: string,
    integrationType: string
) =>
    UrlParamsReplace(
        '/clinic-lab-reports/types',
        { search },
        { clinicId, integrationType }
    );

// Delete idexx test list item
export const DELETE_IDEXX_TEST_LIST_ITEM = (clinicIdexxTestItemId: string) => {
    return UrlParamsReplace(
        '/clinic-integrations/labReport/:clinicIdexxTestItemId',
        {
            clinicIdexxTestItemId,
        }
    );
};

// Create an IDEXX order
export const CREATE_IDEXX_ORDER = () =>
    UrlParamsReplace('/clinic-integrations/create');

// Cancel an IDEXX order
export const CANCEL_IDEXX_ORDER = (clinicId: string, idexxOrderId: string) =>
    UrlParamsReplace(
        '/clinic-integrations/clinic/:clinicId/cancel/:idexxOrderId',
        { clinicId, idexxOrderId }
    );

// Check if IDEXX orders can be deleted by verifying their status
export const CHECK_IDEXX_ORDERS_DELETION_ELIGIBILITY = (clinicId: string) =>
    UrlParamsReplace(
        '/clinic-integrations/clinic/:clinicId/check-deletion-eligibility',
        { clinicId }
    );

export const UPDATE_CLINIC_LAB_REPORT = (id: string) => {
    return UrlParamsReplace('/clinic-lab-reports/:id', { id });
};

export const CREATE_EMR = UrlParamsReplace('/emr');
export const SEND_INDIVIDUAL_EMR = (
    appointmentId: string,
    shareMode: string,
    documentType: string,
    params?: { fileKeys?: string }
) =>
    UrlParamsReplace(
        `/emr/documents/:appointmentId`,
        { appointmentId },
        {
            shareMode,
            documentType,
            ...(params?.fileKeys ? { fileKeys: params.fileKeys } : {}),
        }
    );

export const SEND_VACCINATION_DOCUMENTS = (
    fileKey: string,
    shareMode: string,
    appointmentId: string,
    clinicId: string,
    brandId: string,
    patientId: string,
    email?: string,
    phoneNumber?: string,
    recipientType?: 'client' | 'other'
) => {
    // Use Record<string, any> to allow any properties
    let queryParams: Record<string, any> = {
        fileKey,
        shareMode,
        appointmentId,
        clinicId,
        brandId,
        patientId,
    };

    // Add custom recipient parameters if provided
    if (recipientType === 'other') {
        queryParams = {
            ...queryParams,
            recipientType,
            ...(email ? { email } : {}),
            ...(phoneNumber ? { phoneNumber } : {}),
        };
    }

    return UrlParamsReplace(`/emr/vaccinations`, {}, queryParams);
};

export const SEND_INVOICE_TAB_DOCUMENT = (
    patientId: string,
    fileKeys: string[],
    shareMode: string
) =>
    UrlParamsReplace(
        `/emr/documents/invoices/:patientId`,
        { patientId },
        { fileKeys: JSON.stringify(fileKeys), shareMode }
    );

export const SEND_MEDICAL_RECORD_DOCUMENTS = (
    patientId: string,
    shareMode: string,
    documentType: string
) =>
    UrlParamsReplace(
        `/emr/patient/medical-records/:patientId`,
        { patientId },
        { shareMode, documentType }
    );

export const DOCUMENT_AVAILABLE_FOR_APPOINTMENT = (appointmentId: string) =>
    UrlParamsReplace(
        `/emr/patient/document-availabe/:appointmentId`,
        { appointmentId },
        {}
    );

export const DOCUMENT_AVAILABLE_FOR_PATIENT = (patientId: string) =>
    UrlParamsReplace(
        `/emr/patient/document-availabe-patient/:patientId`,
        { patientId },
        {}
    );

export const FIND_SINGLE_EMR = (appointmentId: string) =>
    UrlParamsReplace(`/emr/individual/:appointmentId`, { appointmentId });

// AI Integration
export const GENERATE_SOAP_NOTES = () => UrlParamsReplace('/ai/soap');

export const DOWNLOAD_TODAYS_APPOINTMENT = (date: string, clinicId: string) =>
    UrlParamsReplace(
        '/appointments/clinic/today-Appointment',
        {},
        { date, clinicId }
    );

// Patients-Reminder
export const CREATE_PATIENT_REMINDER = (patientId: string) =>
    UrlParamsReplace('/patients-reminders/:patientId', { patientId });

export const GET_PATIENT_REMINDERS = (
    patientId: string,
    page: number,
    limit: number
) =>
    UrlParamsReplace(
        '/patients-reminders/:patientId',
        { patientId },
        { page, limit }
    );

export const DELETE_PATIENT_REMINDER = (
    patientId: string,
    reminderId: string
) =>
    UrlParamsReplace('/patients-reminders/:patientId/:id', {
        patientId,
        id: reminderId,
    });

export const COMPLETE_PATIENT_REMINDER = (
    patientId: string,
    reminderId: string
) =>
    UrlParamsReplace('/patients-reminders/:patientId/:id/complete', {
        patientId,
        id: reminderId,
    });

export const OVERRIDDEN_PATIENT_REMINDER = (
    patientId: string,
    reminderId: string
) =>
    UrlParamsReplace('/patients-reminders/:patientId/:id/overridden', {
        patientId,
        id: reminderId,
    });

export const UPDATE_REMINDER = (patientId: string, reminderId: string) =>
    UrlParamsReplace('/patients-reminders/:patientId/:reminderId', {
        patientId,
        reminderId,
    });

export const MARK_REMINDER_INCOMPLETE = (
    patientId: string,
    reminderId: string
) =>
    UrlParamsReplace('/patients-reminders/:patientId/:id/incomplete', {
        patientId,
        id: reminderId,
    });

//document library

export const CREATE_DOCUMENT_LIBRARY = UrlParamsReplace('/document-library');

export const GET_DOCUMENT_LIBRARY = (
    clinicId: string,
    page: number = 1,
    limit: number = 10,
    search: string = ''
) =>
    UrlParamsReplace(
        '/document-library',
        {},
        { page, limit, search, clinicId }
    );

export const DELETE_DOCUMENT_LIBRARY = (id: string) =>
    UrlParamsReplace('/document-library/:id', { id });

export const UPDATE_DOCUMENT_LIBRARY = (id: string) =>
    UrlParamsReplace('/document-library/:id', { id });

export const SEND_DOCUMENT_LIBRARY_TO_PATIENT_OWNER = UrlParamsReplace(
    '/patient-document-libraries'
);

export const GET_DOCUMENT_LIBRARY_TO_PATIENT_OWNER = (
    page: number = 1,
    limit: number = 10,
    search: string = '',
    patientId: string
) =>
    UrlParamsReplace(
        '/patient-document-libraries/:patientId',
        { patientId },
        { page, limit, search }
    );

export const GET_SINGLE_PATIENT_DOCUMENT_LIBRARY = (id: string) =>
    UrlParamsReplace(`/patient-document-libraries/document/:id`, { id }, {});

export const SEND_SINGED_PATIENT_DOCUMENT_LIBRARY = (id: string) =>
    UrlParamsReplace(`/patient-document-libraries/:id`, { id }, {});

export const CREATE_DIAGNOSTIC_NOTES = (
    id: string,
    operation: 'insert' | 'edit' | 'delete'
) =>
    UrlParamsReplace(
        `/clinic-lab-reports/:id/diagnostic-notes/create`,
        { id },
        { operation }
    );
export const DOWNLOAD_ANALYTICS_REPORT = (dto: {
    type: string;
    startDate: string;
    endDate: string;
    clinicId: string;
    reportType?: string;
}) =>
    UrlParamsReplace(
        '/analytics/download-report',
        {},
        {
            type: dto.type,
            startDate: dto.startDate,
            endDate: dto.endDate,
            clinicId: dto.clinicId,
            ...(dto.reportType ? { reportType: dto.reportType } : {}),
        }
    );

export const GET_REVENUE_CHART_DATA = (dto: {
    startDate: string;
    endDate: string;
    clinicId: string;
}) =>
    UrlParamsReplace(
        '/analytics/revenue-chart-data',
        {},
        {
            startDate: dto.startDate,
            endDate: dto.endDate,
            clinicId: dto.clinicId,
        }
    );

export const CREATE_GLOBAL_REMINDER = () =>
    UrlParamsReplace('/global-reminders');

export const GET_GLOBAL_REMINDERS = (
    clinicId: string,
    page: number,
    limit: number,
    search: string
) =>
    UrlParamsReplace(
        '/global-reminders/clinic/:clinicId',
        { clinicId },
        { page, limit, search }
    );

export const UPDATE_GLOBAL_REMINDER = (reminderId: string) =>
    UrlParamsReplace('/global-reminders/:id', { id: reminderId });

export const DELETE_GLOBAL_REMINDER = (reminderId: string) =>
    UrlParamsReplace('/global-reminders/:id', { id: reminderId });

// Diagnostic Notes
export const CREATE_DIAGNOSTIC_TEMPLATE = () =>
    UrlParamsReplace('/diagnostic-templates');

export const GET_DIAGNOSTIC_TEMPLATES = (clinicId: string) =>
    UrlParamsReplace('/diagnostic-templates', {}, { clinicId });

export const UPDATE_DIAGNOSTIC_TEMPLATE = (
    templateId: string,
    clinicId: string
) => UrlParamsReplace(`/diagnostic-templates/${templateId}`, {}, { clinicId });

export const DELETE_DIAGNOSTIC_TEMPLATE = (
    templateId: string,
    clinicId: string
) => UrlParamsReplace(`/diagnostic-templates/${templateId}`, {}, { clinicId });

// Diagnostic Note
export const CREATE_DIAGNOSTIC_NOTE = () =>
    UrlParamsReplace('/diagnostic-templates/diagnostic-note');

export const GET_DIAGNOSTIC_NOTES = (labReportId: string, clinicId: string) =>
    UrlParamsReplace(
        '/diagnostic-templates/lab-report/:labReportId',
        { labReportId },
        { clinicId }
    );

export const GET_DIAGNOSTIC_NOTE = (noteId: string) =>
    UrlParamsReplace('/diagnostic-templates/diagnostic-note/note/:noteId', {
        noteId,
    });

export const UPDATE_DIAGNOSTIC_NOTE = (noteId: string) =>
    UrlParamsReplace('/diagnostic-templates/diagnostic-note/:noteId', {
        noteId,
    });

export const DELETE_DIAGNOSTIC_NOTE = (noteId: string) =>
    UrlParamsReplace('/diagnostic-templates/diagnostic-note/:noteId', {
        noteId,
    });

export const GET_DIAGNOSTIC_NOTES_FOR_PATIENT = (patientId: string) =>
    UrlParamsReplace('/diagnostic-templates/diagnostic-note/:patientId', {
        patientId,
    });

// Templates for specific lab report
export const GET_LAB_REPORT_TEMPLATES = (
    clinicLabReportId: string,
    clinicId: string
) =>
    UrlParamsReplace(
        '/diagnostic-templates/clinic-lab-report/:clinicLabReportId',
        { clinicLabReportId },
        { clinicId }
    );
export const GET_OWNER_PATIENTS = (id: string) =>
    UrlParamsReplace('/owners/:id/patients', { id });

export const GET_CLINIC_OWNERS = (
    clinicId: string,
    page: number = 1,
    limit: number = 10,
    search: string = ''
) => {
    const params: any = { page, limit };
    if (search) {
        params.search = search;
    }
    return UrlParamsReplace('/owners/clinic/:clinicId', { clinicId }, params);
};

export const TRANSFER_OWNERSHIP = () => UrlParamsReplace('/owners/transfer');

export const UPDATE_OWNER = (id: string) =>
    UrlParamsReplace('/owners/:id', { id });

export const CREATE_OWNER = () => UrlParamsReplace('/owners');

export const REMOVE_OWNER_FROM_PATIENT = (ownerId: string, patientId: string) =>
    UrlParamsReplace('/owners/:ownerId/patient/:patientId', {
        ownerId,
        patientId,
    });
export const CREATE_PATIENT_TREATMENT_ESTIMATE =
    UrlParamsReplace('/patient-estimates');

export const GET_PATIENT_TREATMENT_ESTIMATE = (id: string) =>
    UrlParamsReplace('/patient-estimates/:id', { id });

export const GET_TREATMENT_ESTIMATE_FOR_PATIENT = (
    page: number = 1,
    limit: number = 10,
    search: string = '',
    patientId: string
) =>
    UrlParamsReplace(
        '/patient-estimates/patient/:patientId',
        { patientId },
        { page, limit, search }
    );

export const SEND_SIGNED_TREATMENT_ESTIMATE = (id: string) =>
    UrlParamsReplace('/patient-estimates/:id', { id });

export const SHARE_EMR_DOCUMENT = (
    appointmentId: string,
    shareMode: string,
    type: 'client' | 'other',
    email: string,
    phoneNumber: string
) =>
    UrlParamsReplace(
        `/emr/documents/emr/:appointmentId`,
        { appointmentId },
        { shareMode, type, email, phoneNumber }
    );
export const GET_COLLECTED_PAYMENTS_CHART_DATA = (dto: {
    startDate: string;
    endDate: string;
    clinicId: string;
}) =>
    UrlParamsReplace(
        '/analytics/collected-payments-chart-data',
        {},
        {
            startDate: dto.startDate,
            endDate: dto.endDate,
            clinicId: dto.clinicId,
        }
    );

export const GET_APPOINTMENTS_CHART_DATA = (dto: {
    startDate: string;
    endDate: string;
    clinicId: string;
    type: string;
}) =>
    UrlParamsReplace(
        '/analytics/appointments-chart-data',
        {},
        {
            startDate: dto.startDate,
            endDate: dto.endDate,
            clinicId: dto.clinicId,
            type: dto.type,
        }
    );

export const SHARE_PRESCRIPTION_DOCUMENT = (
    appointmentId: string,
    shareMode: string,
    type: 'client' | 'other',
    email: string,
    phoneNumber: string
) =>
    UrlParamsReplace(
        `/emr/documents/prescription/:appointmentId`,
        { appointmentId },
        { shareMode, type, email, phoneNumber }
    );

export const SHARE_SUPPORTING_DOCUMENT = (
    appointmentId: string,
    shareMode: string,
    type: 'client' | 'other',
    email: string,
    phoneNumber: string
) =>
    UrlParamsReplace(
        `/emr/documents/supporting-documents/:appointmentId`,
        { appointmentId },
        { shareMode, type, email, phoneNumber }
    );

export const CREATE_PRESCRIPTION_DOCUMENT = UrlParamsReplace(
    `/emr/create-prescription`
);

export const FIND_PRESCRIPTION_FILEKEY = (appointmentId: string) =>
    UrlParamsReplace(`/emr/prescription/:appointmentId`, { appointmentId });
export const GET_DOCTOR_SUMMARY = (dto: {
    startDate: string;
    endDate: string;
    clinicId: string;
}) =>
    UrlParamsReplace(
        '/analytics/doctor-summary',
        {},
        {
            startDate: dto.startDate,
            endDate: dto.endDate,
            clinicId: dto.clinicId,
        }
    );

export const GET_SUMMARY = (dto: {
    startDate: string;
    endDate: string;
    clinicId: string;
}) =>
    UrlParamsReplace(
        '/analytics/summary',
        {},
        {
            startDate: dto.startDate,
            endDate: dto.endDate,
            clinicId: dto.clinicId,
        }
    );

export const SHARE_LEDGER_DOCUMENT = (
    ownerId: string,
    shareMode: string,
    type: 'client' | 'other',
    email: string,
    phoneNumber: string,
    filters?: {
        userId?: string;
        status?: string;
        startDate?: string;
        endDate?: string;
        searchTerm?: string;
        paymentMode?: string;
    }
) => {
    const queryParams: Record<string, string> = {
        shareMode,
        type,
        email,
        phoneNumber,
        ownerId,
    };

    // Add filter parameters if provided
    if (filters?.userId) queryParams.userId = filters.userId;
    if (filters?.status) queryParams.status = filters.status;
    if (filters?.startDate) queryParams.startDate = filters.startDate;
    if (filters?.endDate) queryParams.endDate = filters.endDate;
    if (filters?.searchTerm) queryParams.searchTerm = filters.searchTerm;
    if (filters?.paymentMode) queryParams.paymentMode = filters.paymentMode;

    return UrlParamsReplace(`/emr/ledger`, {}, queryParams);
};

export const DOWNLOAD_LEDGER_DOCUMENT = (
    ownerId: string,
    filters?: {
        userId?: string;
        status?: string;
        startDate?: string;
        endDate?: string;
        searchTerm?: string;
        paymentMode?: string;
    }
) => {
    const queryParams: Record<string, string> = { ownerId };

    // Add filter parameters if provided
    if (filters?.userId) queryParams.userId = filters.userId;
    if (filters?.status) queryParams.status = filters.status;
    if (filters?.startDate) queryParams.startDate = filters.startDate;
    if (filters?.endDate) queryParams.endDate = filters.endDate;
    if (filters?.searchTerm) queryParams.searchTerm = filters.searchTerm;
    if (filters?.paymentMode) queryParams.paymentMode = filters.paymentMode;

    return UrlParamsReplace(`/emr/store/ledger`, {}, queryParams);
};

export const LEDGER_DOCUMENT_STATUS_URL = (requestId: string) =>
    UrlParamsReplace(`/emr/ledger/status/:requestId`, { requestId }, {});

export const GET_INDIVIDUAL_PAYEMENT_DETAIL = (id: string) =>
    UrlParamsReplace(`/payment-details/findOne/:id`, { id }, {});

export const DELETE_LEDGER_DOCUMENT_FILEKEY = (id: string) =>
    UrlParamsReplace('/payment-details/delete-ledger/:id', { id }, {});

export const GET_LAST_ACTIVITY = (tabName: string, reference_id: string) =>
    UrlParamsReplace('/tab-activities/last/:tabName/:referenceId', {
        tabName,
        referenceId: reference_id,
    });

export const CREATE_TAB_ACTIVITY = () =>
    UrlParamsReplace('/tab-activities', {});

// Get owner invoices with payments (with filters)
export const GET_OWNER_INVOICES_WITH_PAYMENTS = (
    ownerId: string,
    page: number = 1,
    limit: number = 10,
    filters?: {
        startDate?: string;
        endDate?: string;
        petName?: string;
        status?: string;
        paymentMode?: string;
        searchTerm?: string;
        userId?: string;
        invoiceType?: string;
    }
) => {
    const queryParams: Record<string, string> = {
        page: page.toString(),
        limit: limit.toString(),
    };

    if (filters?.startDate) queryParams.startDate = filters.startDate;
    if (filters?.endDate) queryParams.endDate = filters.endDate;
    if (filters?.petName) queryParams.petName = filters.petName;
    if (filters?.status) queryParams.status = filters.status;
    if (filters?.paymentMode) queryParams.paymentMode = filters.paymentMode;
    if (filters?.searchTerm) queryParams.searchTerm = filters.searchTerm;
    if (filters?.userId) queryParams.userId = filters.userId;
    if (filters?.invoiceType) queryParams.invoiceType = filters.invoiceType;

    return UrlParamsReplace(
        '/payment-details/owner-invoices/:ownerId',
        { ownerId },
        queryParams
    );
};

// Get pending invoices for an owner (with filtering options)
export const GET_OWNER_PENDING_INVOICES = (
    ownerId: string,
    filters?: {
        startDate?: string;
        endDate?: string;
        petName?: string;
        searchTerm?: string;
    }
) => {
    return UrlParamsReplace(
        '/payment-details/pending-invoices/:ownerId',
        { ownerId },
        filters
    );
};

// Get owner ledger (combined invoices and payments chronologically)
export const GET_OWNER_LEDGER = (ownerId: string) => {
    return UrlParamsReplace('/payment-details/owner-ledger/:ownerId', {
        ownerId,
    });
};

// Update document URL helpers to match existing API endpoints
export const INVOICE_DOCUMENT_URL = (
    referenceAlphaId: string,
    action: 'download' | 'share',
    patientId: string,
    shareMethod?: 'email' | 'whatsapp' | 'both',
    recipient?: 'client' | 'other',
    email?: string,
    whatsapp?: string
) => {
    const queryParams: Record<string, string> = {
        action,
        patientId,
    };

    if (action === 'share' && shareMethod) {
        queryParams.shareMethod = shareMethod;

        // Add recipient type if provided
        if (recipient) {
            queryParams.recipient = recipient;
        }

        // Add email and whatsapp values if provided
        if (email) {
            queryParams.email = email;
        }

        if (whatsapp) {
            queryParams.phoneNumber = whatsapp;
        }
    }

    return UrlParamsReplace(
        '/invoices/documents/:referenceAlphaId',
        { referenceAlphaId },
        queryParams
    );
};

// New endpoint for checking invoice document status (polling)
export const INVOICE_DOCUMENT_STATUS_URL = (invoiceId: string) => {
    return UrlParamsReplace('/invoices/document-status/:invoiceId', {
        invoiceId,
    });
};

export const PAYMENT_DOCUMENT_STATUS_URL = (referenceAlphaId: string) => {
    return UrlParamsReplace(
        `/payment-details/document-status/:referenceAlphaId`,
        { referenceAlphaId }
    );
};

export const PAYMENT_DOCUMENT_URL = (
    referenceAlphaId: string,
    documentType: 'creditnote' | 'payment-details',
    action: 'download' | 'share',
    shareMethod?: 'email' | 'whatsapp' | 'both',
    recipient?: 'client' | 'other',
    email?: string,
    whatsapp?: string
) => {
    const queryParams: Record<string, string> = {
        documentType,
        action,
    };

    if (action === 'share' && shareMethod) {
        queryParams.shareMethod = shareMethod;

        // Add recipient type if provided
        if (recipient) {
            queryParams.recipient = recipient;
        }

        // Add email and whatsapp values if provided
        if (email) {
            queryParams.email = email;
        }

        if (whatsapp) {
            queryParams.phoneNumber = whatsapp;
        }
    }

    return UrlParamsReplace(
        '/payment-details/documents/:referenceAlphaId',
        { referenceAlphaId },
        queryParams
    );
};

// Client Booking Settings
export const GET_CLIENT_BOOKING_SETTINGS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId/client-booking-settings', {
        clinicId,
    });

export const UPDATE_CLIENT_BOOKING_SETTINGS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId/client-booking-settings', {
        clinicId,
    });
// End Client Booking Settings

// Clinic Settings
export const GET_CLINIC_SETTINGS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId/settings', {
        clinicId,
    });

export const UPDATE_CLINIC_SETTINGS = (clinicId: string) =>
    UrlParamsReplace('/clinics/:clinicId/settings', {
        clinicId,
    });
// End Clinic Settings
// --- Payment Receipts URLs --- Start ---
export const SHARE_PAYMENT_RECEIPTS_URL = (
    patientId: string,
    shareMode: string, // JSON string array, e.g., '["email","whatsapp"]'
    type: 'client' | 'other',
    email: string, // Required if type is 'other' and email is in shareMode
    phoneNumber: string // Required if type is 'other' and whatsapp is in shareMode
) =>
    UrlParamsReplace(
        `/emr/receipts/share`, // Matches the backend controller path
        {},
        { patientId, shareMode, type, email, phoneNumber }
    );

export const STORE_PAYMENT_RECEIPTS_URL = (patientId: string) =>
    UrlParamsReplace(`/emr/receipts/store`, {}, { patientId }); // Matches the backend controller path

export const PAYMENT_RECEIPTS_STATUS_URL = (requestId: string) =>
    UrlParamsReplace(`/emr/receipts/status/:requestId`, { requestId }, {}); // Matches the backend controller path
// --- Payment Receipts URLs --- End ---

// --- Statement URLs --- Start ---
export const REQUEST_STATEMENT_DOCUMENTS = (
    ownerId: string,
    types: string[],
    action: 'DOWNLOAD' | 'SHARE',
    shareMethod?: 'email' | 'whatsapp' | 'both',
    recipient?: 'client' | 'other',
    email?: string,
    phoneNumber?: string,
    filters?: {
        userId?: string;
        status?: string;
        startDate?: string;
        endDate?: string;
        searchTerm?: string;
        paymentMode?: string;
    }
) => {
    const queryParams: Record<string, string> = {
        types: types.join(','),
        action,
    };

    // Add share-specific parameters if action is SHARE
    if (action === 'SHARE' && shareMethod) {
        queryParams.shareMethod = shareMethod;

        if (recipient) {
            queryParams.recipient = recipient;

            // Add email and phoneNumber if recipient is 'other'
            if (recipient === 'other') {
                if (email) queryParams.email = email;
                if (phoneNumber) queryParams.phoneNumber = phoneNumber;
            }
        }
    }

    // Add filter parameters if provided
    if (filters) {
        if (filters.userId) queryParams.userId = filters.userId;
        if (filters.status) queryParams.status = filters.status;
        if (filters.startDate) queryParams.startDate = filters.startDate;
        if (filters.endDate) queryParams.endDate = filters.endDate;
        if (filters.searchTerm) queryParams.searchTerm = filters.searchTerm;
        if (filters.paymentMode) queryParams.paymentMode = filters.paymentMode;
    }

    return UrlParamsReplace(
        `/statements/owner/:ownerId/documents`,
        { ownerId },
        queryParams
    );
};

export const STATEMENT_DOCUMENT_STATUS_URL = (requestId: string) =>
    UrlParamsReplace(
        `/statements/documents/status/:requestId`,
        { requestId },
        {}
    );
// --- Statement URLs --- End ---

// --- Credits URLs --- Start ---
export const GET_OWNER_CREDIT_TRANSACTIONS = (
    ownerId: string,
    filters?: {
        page?: number;
        limit?: number;
        searchTerm?: string;
        transactionType?: string;
        derivedTransactionTypes?: string;
        startDate?: string;
        endDate?: string;
        userId?: string;
    }
) => {
    const queryParams: Record<string, string> = {};

    if (filters?.page) queryParams.page = filters.page.toString();
    if (filters?.limit) queryParams.limit = filters.limit.toString();
    if (filters?.searchTerm) queryParams.searchTerm = filters.searchTerm;
    if (filters?.transactionType)
        queryParams.transactionType = filters.transactionType;
    if (filters?.derivedTransactionTypes)
        queryParams.derivedTransactionTypes = filters.derivedTransactionTypes;
    if (filters?.startDate) queryParams.startDate = filters.startDate;
    if (filters?.endDate) queryParams.endDate = filters.endDate;
    if (filters?.userId) queryParams.userId = filters.userId;

    return UrlParamsReplace(
        '/credits/owner/:ownerId/transactions-paginated',
        { ownerId },
        queryParams
    );
};
// --- Credits URLs --- End ---

// Edit payment details
export const EDIT_PAYMENT_DETAILS = (paymentId: string) => {
    return UrlParamsReplace('/payment-details/:id', { id: paymentId });
};

// Delete payment details
export const DELETE_PAYMENT_DETAILS = (paymentId: string) => {
    return UrlParamsReplace('/payment-details/:id', { id: paymentId });
};
