import { useEffect, useRef, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { updateUserRoute, getAuth } from '@/app/services/identity.service';

/**
 * Custom hook for tracking and persisting user's current route
 * Automatically updates the backend when the user navigates to different pages
 */
export const useRouteMemory = () => {
    const pathname = usePathname();
    const router = useRouter();
    const lastRouteRef = useRef<string>('');
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);
    const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const retryCountRef = useRef<number>(0);
    const [isOnline, setIsOnline] = useState(true);

    // Monitor network status
    useEffect(() => {
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);

        window.addEventListener('online', handleOnline);
        window.addEventListener('offline', handleOffline);

        return () => {
            window.removeEventListener('online', handleOnline);
            window.removeEventListener('offline', handleOffline);
        };
    }, []);

    useEffect(() => {
        // Check if user is authenticated
        const auth = getAuth();
        if (!auth || !isOnline) {
            return;
        }

        // Skip certain routes that shouldn't be remembered
        const skipRoutes = [
            '/signin/pin',
            '/signin/email',
            '/signin/forgot-password',
            '/onboard',
            '/',
            '/signedDoc',
        ];

        const shouldSkip = skipRoutes.some(
            (route) =>
                pathname === route ||
                pathname.startsWith('/signedDoc/') ||
                pathname.startsWith('/estimate-signed-doc/')
        );

        if (shouldSkip || pathname === lastRouteRef.current) {
            return;
        }

        // Debounce route updates to avoid excessive API calls
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(async () => {
            const updateRouteWithRetry = async (retryCount = 0) => {
                try {
                    const result = await updateUserRoute(pathname);
                    if (result.status) {
                        lastRouteRef.current = pathname;
                        retryCountRef.current = 0; // Reset retry count on success
                    } else if (retryCount < 3 && isOnline) {
                        // Retry up to 3 times with exponential backoff
                        const backoffTime = Math.pow(2, retryCount) * 1000;
                        retryTimeoutRef.current = setTimeout(
                            () => updateRouteWithRetry(retryCount + 1),
                            backoffTime
                        );
                    }
                } catch (error) {
                    console.error('Failed to update route memory:', error);
                    if (retryCount < 3 && isOnline) {
                        // Retry on error as well
                        const backoffTime = Math.pow(2, retryCount) * 1000;
                        retryTimeoutRef.current = setTimeout(
                            () => updateRouteWithRetry(retryCount + 1),
                            backoffTime
                        );
                    }
                }
            };

            await updateRouteWithRetry();
        }, 1000); // 1 second debounce

        // Cleanup timeout on unmount
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
            if (retryTimeoutRef.current) {
                clearTimeout(retryTimeoutRef.current);
            }
        };
    }, [pathname, isOnline]);

    /**
     * Navigate to a route and update the route memory
     * @param route The route to navigate to
     */
    const navigateAndRemember = async (route: string) => {
        router.push(route);
        // The useEffect will handle updating the route memory
    };

    return {
        navigateAndRemember,
        currentRoute: pathname,
    };
};
