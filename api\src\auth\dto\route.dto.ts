import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, MaxLength, Matches } from 'class-validator';

export class UpdateUserRouteDto {
	@ApiProperty({
		description: 'The route path to store for the user',
		example: '/dashboard',
		maxLength: 500
	})
	@IsString()
	@IsNotEmpty()
	@MaxLength(500, { message: 'Route path cannot exceed 500 characters' })
	@Matches(/^\/(?!\/)/, {
		message: 'Route must be a valid path starting with /'
	})
	route!: string;
}
