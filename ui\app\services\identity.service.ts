import Cookies from 'universal-cookie';
import * as Http from './http.service';
import { UPDATE_USER_ROUTE } from './url.service';

const cookies = new Cookies();

export const getAuth = () => {
    const auth = cookies.get('AUTH');
    return auth;
};

export const setAuth = (authObject: any) => {
    cookies.set('AUTH', JSON.stringify(authObject), { path: '/' });
    return authObject;
};

export const removeAuth = () => {
    return cookies.remove('AUTH', { path: '/' });
};

export const setRefreshToken = (refreshToken: string) => {
    cookies.set('RT', refreshToken, { path: '/' });
    return refreshToken;
};

export const getRefreshToken = () => {
    const auth = cookies.get('RT');
    return auth;
};

export const removeRT = () => {
    cookies.remove('RT', { path: '/' });
};

export const isAuthenticated = (user: any) => user != null && user.token;

export const isUnauthorizedRequest = (auth: any) => {
    return !auth || !isAuthenticated(JSON.parse(auth));
};

// New function to get the access token
export const getAccessToken = () => {
    const auth = getAuth();
    if (auth) {
        const parsedAuth = JSON.parse(auth);
        return parsedAuth.token;
    }
    return null;
};

export const getBrandId = () => {
    const brandId = cookies.get('BRAND_ID');
    return brandId;
};

export const setBrandId = (brandId: string, isSecure: boolean = false) => {
    cookies.set('BRAND_ID', brandId, {
        path: '/',
        maxAge: 60 * 60 * 24 * 7, // 7 days
        sameSite: 'lax',
        secure: isSecure,
    });
};

/**
 * Updates the user's current route in the backend
 * @param route The current route path (e.g., '/dashboard')
 * @returns Promise with the API response
 */
export const updateUserRoute = async (route: string) => {
    try {
        return await Http.postWithAuth(UPDATE_USER_ROUTE(), { route });
    } catch (error) {
        console.error('Failed to update user route:', error);
        // Silent failure - don't interrupt user experience for route tracking
        return { status: false };
    }
};
