import Cookies from 'universal-cookie';

const cookies = new Cookies();

export const getAuth = () => {
    const auth = cookies.get('AUTH');
    return auth;
};

export const setAuth = (authObject: any) => {
    cookies.set('AUTH', JSON.stringify(authObject), { path: '/' });
    return authObject;
};

export const removeAuth = () => {
    return cookies.remove('AUTH', { path: '/' });
};

export const setRefreshToken = (refreshToken: string) => {
    cookies.set('RT', refreshToken, { path: '/' });
    return refreshToken;
};

export const getRefreshToken = () => {
    const auth = cookies.get('RT');
    return auth;
};

export const removeRT = () => {
    cookies.remove('RT', { path: '/' });
};

export const isAuthenticated = (user: any) => user != null && user.token;

export const isUnauthorizedRequest = (auth: any) => {
    return !auth || !isAuthenticated(JSON.parse(auth));
};

// New function to get the access token
export const getAccessToken = () => {
    const auth = getAuth();
    if (auth) {
        const parsedAuth = JSON.parse(auth);
        return parsedAuth.token;
    }
    return null;
};

export const getBrandId = () => {
    const brandId = cookies.get('BRAND_ID');
    return brandId;
};


export const setBrandId = (brandId: string, isSecure: boolean = false) => {
    cookies.set('BRAND_ID', brandId, {
        path: '/',
        maxAge: 60 * 60 * 24 * 7, // 7 days
        sameSite: 'lax',
        secure: isSecure
    });
};