import {
	Controller,
	Post,
	Body,
	UseGuards,
	Req,
	HttpException,
	HttpStatus,
	InternalServerErrorException
} from '@nestjs/common';
import {
	ApiTags,
	ApiOperation,
	ApiResponse,
	ApiBearerAuth
} from '@nestjs/swagger';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { UpdateUserRouteDto } from './dto/route.dto';
import { SessionService } from '../session/session.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { RequestWithUser } from './auth.controller';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';
import { WinstonLogger } from '../utils/logger/winston-logger.service';

@ApiTags('Route')
@Controller('auth/user-route')
export class RouteController {
	constructor(
		private readonly sessionService: SessionService,
		@InjectRepository(User)
		private usersRepository: Repository<User>,
		private readonly logger: WinstonLogger
	) {}

	@Post()
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth()
	@ApiOperation({ summary: "Update user's current route" })
	@ApiResponse({
		status: 200,
		description: 'Route updated successfully'
	})
	@ApiResponse({ status: 401, description: 'Unauthorized' })
	@ApiResponse({ status: 500, description: 'Internal server error' })
	@TrackMethod('updateUserRoute')
	async updateUserRoute(
		@Body() updateUserRouteDto: UpdateUserRouteDto,
		@Req() req: RequestWithUser
	) {
		try {
			const userId = req.user.id;
			const { route } = updateUserRouteDto;

			// Database first: Store route in database for persistence
			await this.usersRepository.update(
				{ id: userId },
				{ lastRoute: route }
			);

			// Then cache: Store route in Redis for fast access (24 hours TTL)
			await this.sessionService.setUserRoute(userId, route, 60 * 60 * 24);

			return {
				status: true,
				message: 'Route updated successfully'
			};
		} catch (error) {
			this.logger.error(
				`Failed to update user route for user ${req.user.id}`,
				error
			);

			throw new InternalServerErrorException(
				'Failed to update user route'
			);
		}
	}
}
