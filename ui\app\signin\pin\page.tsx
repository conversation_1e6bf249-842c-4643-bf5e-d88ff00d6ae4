'use client';

import React, { useEffect, useState } from 'react';
import { useLoginWithPinMutation } from '../../services/signin.queries';
import { useRouter } from 'next/navigation';
import PinSignInForm from '../../organisms/signin/PinSignIn';
import Alert from '../../atoms/Alert';
import Link from 'next/link';
import {
    getBrandId,
    setAuth,
    setBrandId,
} from '@/app/services/identity.service';
import { useSearchParams } from 'next/navigation';
import { getBrandBySlug } from '@/app/services/brands.services';

export default function SignUpPage() {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const brandId = getBrandId();

    const [alert, setAlert] = useState<{
        variant: 'info' | 'error' | 'success' | 'warning';
        label: string;
        isOpen: boolean;
        payload?: any;
    }>({ variant: 'info', label: '', isOpen: false });
    const router = useRouter();
    const searchParams = useSearchParams();
    const status = searchParams.get('status');

    useEffect(() => {
        const detectBrandFromHostname = async () => {
            const fullHostname = window.location.hostname;

            const isProduction = fullHostname.includes('nidana.io');
            const isQA = fullHostname.includes('nidanaqa-api.napses.in');
            const isUAT = fullHostname.includes('nidana.tech');
            const isLocal = fullHostname.includes('localhost');

            let currentHost = '';
            let baseDomain = '';

            if (isProduction) {
                baseDomain = 'nidana.io';
            } else if (isQA) {
                baseDomain = 'nidanaqa-api.napses.in';
            } else if (isUAT) {
                baseDomain = 'nidana.tech';
            } else if (isLocal) {
                baseDomain = 'localhost:4201';
            }

            if (baseDomain) {
                currentHost = fullHostname.split('.')[0];
            }

            if (currentHost !== 'superadmin' && currentHost !== '') {
                try {
                    const brandInfo = await getBrandBySlug(
                        currentHost.split('.')[0]
                    );
                    const brandId = brandInfo?.data?.id;
                    const isSecure = window.location.protocol === 'https:';
                    if (brandId) {
                        setBrandId(brandId, isSecure);
                    }
                } catch (error) {
                    console.error('Error fetching brand info:', error);
                }
            }
        };

        detectBrandFromHostname();
    }, []);

    const { loginWithPinMutation } = useLoginWithPinMutation(brandId);

    const handleAlertClose = () => {
        setAlert({ ...alert, isOpen: false });
    };

    const onSubmit = (data: { pin: string }) => {
        setIsSubmitting(true);
        loginWithPinMutation.mutate(data.pin, {
            onSuccess: (response) => {
                console.log('Login response:', response); // Log the entire response

                if (response.status === false) {
                    // Check if it's an inactive clinic error
                    const errorMessage =
                        response?.rawError?.errors ||
                        response?.rawError?.message ||
                        response?.errorMessage ||
                        '';
                    console.log('Error message:', errorMessage);
                    if (
                        errorMessage.includes(
                            'This clinic is currently inactive'
                        ) ||
                        errorMessage.includes('clinic is currently inactive')
                    ) {
                        setAlert({
                            variant: 'error',
                            label: 'Clinic is deactivated. Please contact the admin.',
                            isOpen: false,
                            payload: 'inactiveClinic',
                        });
                    } else {
                        setAlert({
                            variant: 'error',
                            label: 'Invalid PIN entered. Please try again.',
                            isOpen: false,
                            payload: 'wrongPin',
                        });
                    }
                } else if (response.data) {
                    const {
                        globalUserId,
                        userId, // This is clinicUserId from clinic_users table
                        clinicId,
                        role,
                        token,
                        isFirstLogin,
                        isMultiClinic,
                        brandId,
                        username,
                        clinicName,
                        isFullyOnboarded,
                        brandName,
                    } = response.data;

                    setAuth({
                        globalUserId,
                        userId,
                        clinicId,
                        token,
                        role,
                        isFirstLogin,
                        isMultiClinic,
                        brandId,
                        username,
                        clinicName,
                        brandName,
                    });
                    if (role === 'super_admin') {
                        // Super admin should go directly to dashboard, skip clinic selection
                        router.push('/dashboard');
                    } else if (role === 'admin') {
                        if (isFirstLogin || isMultiClinic) {
                            router.push('/onboard'); // Change back to update clinic details with complete flow
                        } else {
                            router.push('/dashboard');
                        }
                    } else {
                        router.push('/onboard');
                    }
                } else {
                    setAlert({
                        variant: 'error',
                        label: 'Unexpected response format. Please try again.',
                        isOpen: true,
                    });
                }
            },
            onError: (error) => {
                console.error('Login error:', error);
                setAlert({
                    variant: 'error',
                    label: 'An error occurred during login. Please try again.',
                    isOpen: true,
                });
            },
            onSettled: () => {
                setIsSubmitting(false);
            },
        });
    };

    return (
        <div className="">
            <PinSignInForm
                onSubmit={onSubmit}
                isSubmitting={isSubmitting}
                alert={alert}
                status={status}
            />

            <div className="absolute z-50 top-5 w-[450px] left-1/2 -translate-x-1/2">
                {alert.isOpen && (
                    <Alert
                        variant={alert.variant}
                        label={alert.label}
                        onClose={handleAlertClose}
                        className="mb-4"
                    />
                )}
            </div>
        </div>
    );
}
