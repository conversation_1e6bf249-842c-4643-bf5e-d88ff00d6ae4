"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const clinic_entity_1 = require("./entities/clinic.entity");
const user_entity_1 = require("../users/entities/user.entity");
const common_2 = require("@nestjs/common");
const typeorm_2 = require("typeorm");
const clinic_room_entity_1 = require("./entities/clinic-room.entity");
const read_service_1 = require("../utils/excel/read.service");
const format_service_1 = require("../utils/excel/format.service");
const clinic_consumbles_service_1 = require("../clinic-consumables/clinic-consumbles.service");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const clinic_medications_service_1 = require("../clinic-medications/clinic-medications.service");
const clinic_products_service_1 = require("../clinic-products/clinic-products.service");
const clinic_services_service_1 = require("../clinic-services/clinic-services.service");
const clinic_vaccinations_service_1 = require("../clinic-vaccinations/clinic-vaccinations.service");
const clinic_lab_report_service_1 = require("../clinic-lab-report/clinic-lab-report.service");
const XLSX = require("xlsx");
const bcrypt = require("bcrypt");
const inventory_headers_enum_1 = require("./enum/inventory-headers.enum");
const users_service_1 = require("../users/users.service");
const send_mail_service_1 = require("../utils/aws/ses/send-mail-service");
const clinic_user_entity_1 = require("./entities/clinic-user.entity");
const constants_1 = require("../utils/constants");
const get_login_url_1 = require("../utils/common/get-login-url");
const brands_service_1 = require("../brands/brands.service");
let ClinicService = class ClinicService {
    constructor(clinicRepository, clinicRoomRepository, clinicUserRepository, logger, consumblesService, clinicMedicationsService, productsService, clinicServices, vaccinationService, clinicLabReportService, userService, mailService, dataSource, brandService) {
        this.clinicRepository = clinicRepository;
        this.clinicRoomRepository = clinicRoomRepository;
        this.clinicUserRepository = clinicUserRepository;
        this.logger = logger;
        this.consumblesService = consumblesService;
        this.clinicMedicationsService = clinicMedicationsService;
        this.productsService = productsService;
        this.clinicServices = clinicServices;
        this.vaccinationService = vaccinationService;
        this.clinicLabReportService = clinicLabReportService;
        this.userService = userService;
        this.mailService = mailService;
        this.dataSource = dataSource;
        this.brandService = brandService;
    }
    async createClinic(createClinicDto, createdBy) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            this.logger.log('Creating new clinic', { dto: createClinicDto });
            const { name, brandId, adminFirstName, adminLastName, adminEmail, adminMobile } = createClinicDto;
            console.log({
                name,
                brandId,
                adminFirstName,
                adminLastName,
                adminEmail,
                adminMobile
            });
            const existingClinic = await queryRunner.manager.findOne(clinic_entity_1.ClinicEntity, { where: { name } });
            if (existingClinic) {
                throw new common_1.ConflictException(`The clinic with name ${name} already exists`);
            }
            const existingUser = await queryRunner.manager.findOne(user_entity_1.User, {
                where: { email: adminEmail }
            });
            if (existingUser) {
                throw new common_1.ConflictException(`An Admin with email ${adminEmail} already exists`);
            }
            const pin = await this.userService.generateUniquePin();
            const hashedPin = await bcrypt.hash(pin, 10);
            const newUser = queryRunner.manager.create(user_entity_1.User, {
                firstName: adminFirstName,
                lastName: adminLastName,
                email: adminEmail,
                roleId: constants_1.ADMIN_ROLE_ID,
                mobileNumber: adminMobile,
                pin: hashedPin,
                createdBy,
                updatedBy: createdBy
            });
            await queryRunner.manager.save(newUser);
            const newClinic = queryRunner.manager.create(clinic_entity_1.ClinicEntity, {
                name,
                brandId,
                adminFirstName,
                adminLastName,
                adminEmail,
                adminMobile,
                createdBy,
                updatedBy: createdBy
            });
            await queryRunner.manager.save(newClinic);
            const clinicId = newClinic.id;
            const userId = newUser.id;
            const newClinicUser = queryRunner.manager.create(clinic_user_entity_1.ClinicUser, {
                clinicId,
                userId,
                isPrimary: true,
                brandId,
                createdBy,
                updatedBy: createdBy
            });
            await queryRunner.manager.save(newClinicUser);
            const brandInfo = await this.brandService.getBrandById(brandId);
            const loginUrl = (0, get_login_url_1.getLoginUrl)(brandInfo === null || brandInfo === void 0 ? void 0 : brandInfo.slug); //'https://nidanaqa-api.napses.in/sigin/pin';
            const subject = 'Clinic Registration';
            const body = `
				Dear ${adminFirstName} ${adminLastName},
				Your clinic has been registered with us. Your PIN is: ${pin}. Please use this PIN to log in to your account.
				The URL to login to your clinic is ${loginUrl}
			`;
            console.log(body);
            if ((0, get_login_url_1.isProduction)() && adminEmail) {
                await this.mailService.sendMail({
                    body,
                    subject,
                    toMailAddress: adminEmail
                });
            }
            else if (!(0, get_login_url_1.isProduction)()) {
                await this.mailService.sendMail({
                    body,
                    subject,
                    toMailAddress: constants_1.DEV_SES_EMAIL //adminEmail
                });
            }
            await queryRunner.commitTransaction();
            return newClinic;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error('Error creating new clinic', { error });
            if (error instanceof common_1.ConflictException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('An unexpected error occurred while creating/associating the user');
        }
        finally {
            await queryRunner.release();
        }
    }
    async getAllClinics(page = 1, limit = 10, orderBy = 'DESC') {
        const [clinics, total] = await this.clinicRepository.findAndCount({
            where: { deletedAt: (0, typeorm_2.IsNull)() },
            relations: ['brand'],
            skip: (page - 1) * limit,
            take: limit,
            order: orderBy === 'ASC' ? { createdAt: 'ASC' } : { createdAt: 'DESC' }
        });
        return { clinics, total };
    }
    async updateBasicClinicInfo(id, updateBasicClinicDto, userId) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const clinic = await queryRunner.manager.findOne(clinic_entity_1.ClinicEntity, {
                where: { id }
            });
            if (!clinic) {
                throw new common_2.NotFoundException(`Clinic with ID "${id}" not found`);
            }
            const clinicUser = await queryRunner.manager.findOne(clinic_user_entity_1.ClinicUser, {
                where: { clinicId: clinic.id, isPrimary: true },
                relations: ['user']
            });
            if (!clinicUser) {
                throw new common_2.NotFoundException(`Primary admin for Clinic with ID "${id}" not found`);
            }
            const user = await queryRunner.manager.findOne(user_entity_1.User, {
                where: { id: clinicUser.userId }
            });
            let isEmailChanged = false;
            if (user && user.email !== updateBasicClinicDto.adminEmail) {
                isEmailChanged = true;
                const existingUser = await queryRunner.manager.findOne(user_entity_1.User, {
                    where: { email: updateBasicClinicDto.adminEmail }
                });
                if (existingUser) {
                    throw new common_1.ConflictException(`An admin with email ${updateBasicClinicDto.adminEmail} already exists`);
                }
            }
            if (user) {
                user.firstName = updateBasicClinicDto.adminFirstName;
                user.lastName = updateBasicClinicDto.adminLastName;
                user.mobileNumber = updateBasicClinicDto.adminMobile;
                user.updatedBy = userId;
            }
            if (user && isEmailChanged) {
                user.email = updateBasicClinicDto.adminEmail;
            }
            await queryRunner.manager.save(user);
            Object.assign(clinic, updateBasicClinicDto);
            await queryRunner.manager.save(clinic);
            if (isEmailChanged) {
                const subject = 'Your New Admin Credentials';
                const body = `
					Dear ${updateBasicClinicDto.adminFirstName} ${updateBasicClinicDto.adminLastName},
	
					Your new password is 8907. Please use the following link to log in.
				`;
                await this.mailService.sendMail({
                    body,
                    subject,
                    toMailAddress: updateBasicClinicDto.adminEmail
                });
            }
            await queryRunner.commitTransaction();
            return clinic;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error('Error updating clinic', { error });
            if (error instanceof common_1.ConflictException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('An unexpected error occurred while updating the clinic and associated user');
        }
        finally {
            await queryRunner.release();
        }
    }
    async updateClinic(id, updateClinicDto, userId) {
        try {
            const clinic = await this.clinicRepository.findOne({
                where: { id }
            });
            if (!clinic) {
                throw new common_2.NotFoundException(`Clinic with ID "${id}" not found`);
            }
            // Handle phone numbers separately
            if (updateClinicDto.phoneNumbers) {
                clinic.phoneNumbers = updateClinicDto.phoneNumbers
                    .filter(phone => phone.country_code && phone.number)
                    .map(phone => ({
                    country_code: phone.country_code,
                    number: phone.number
                }));
            }
            // Handle customRule separately with validation
            if (updateClinicDto.customRule !== undefined) {
                if (typeof updateClinicDto.customRule
                    .patientLastNameAsOwnerLastName !== 'boolean') {
                    throw new common_1.BadRequestException('patientLastNameAsOwnerLastName must be a boolean value');
                }
                clinic.customRule = {
                    patientLastNameAsOwnerLastName: updateClinicDto.customRule
                        .patientLastNameAsOwnerLastName
                };
            }
            const { ...restOfDto } = updateClinicDto;
            Object.assign(clinic, restOfDto);
            clinic.updatedBy = userId;
            const updatedClinic = await this.clinicRepository.save(clinic);
            // Ensure customRule is included in response
            return {
                ...updatedClinic,
                customRule: updatedClinic.customRule || {
                    patientLastNameAsOwnerLastName: false
                }
            };
        }
        catch (error) {
            this.logger.error('Error updating clinic', { error });
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('An unexpected error occurred while updating the clinic');
        }
    }
    async getClinicById(id) {
        const clinic = await this.clinicRepository.findOne({ where: { id } });
        if (!clinic) {
            throw new common_2.NotFoundException(`This clinic with ${id} doesn't exist`);
        }
        // Ensure customRule is included in response with default value if not set
        return {
            ...clinic,
            customRule: clinic.customRule || {
                patientLastNameAsOwnerLastName: false
            }
        };
    }
    async getClinicRooms(id) {
        const clinic = await this.clinicRepository.findOne({ where: { id } });
        if (!clinic) {
            throw new common_2.NotFoundException(`This clinic with ${id} doesn't exist`);
        }
        const [rooms, total] = await this.clinicRoomRepository.findAndCount({
            where: { clinicId: id }
        });
        return { rooms, total };
    }
    async createClinicRoom(createClinicRoomDto, brandId) {
        const clinic = await this.clinicRepository.findOne({
            where: { id: createClinicRoomDto.clinicId }
        });
        if (!clinic) {
            throw new common_2.NotFoundException(`Clinic with ID ${createClinicRoomDto.clinicId} not found`);
        }
        const newRoom = this.clinicRoomRepository.create({
            ...createClinicRoomDto,
            brandId: brandId
        });
        return await this.clinicRoomRepository.save(newRoom);
    }
    async updateClinicRoom(id, updateClinicRoomDto) {
        const room = await this.clinicRoomRepository.findOne({ where: { id } });
        if (!room) {
            throw new common_2.NotFoundException(`Clinic room with ID ${id} not found`);
        }
        if (updateClinicRoomDto.clinicId) {
            const clinic = await this.clinicRepository.findOne({
                where: { id: updateClinicRoomDto.clinicId }
            });
            if (!clinic) {
                throw new common_2.NotFoundException(`Clinic with ID ${updateClinicRoomDto.clinicId} not found`);
            }
        }
        Object.assign(room, updateClinicRoomDto);
        return await this.clinicRoomRepository.save(room);
    }
    async deleteRoom(id) {
        const result = await this.clinicRoomRepository.delete(id);
        if (result.affected === 0) {
            throw new common_2.NotFoundException(`Room with ID "${id}" not found`);
        }
    }
    async deactivateClinic(id) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const clinic = await queryRunner.manager.findOne(clinic_entity_1.ClinicEntity, {
                where: { id, deletedAt: (0, typeorm_2.IsNull)() }
            });
            if (!clinic) {
                throw new common_2.NotFoundException(`Clinic with id ${id} not found`);
            }
            if (!clinic.isActive) {
                throw new common_1.ConflictException(`Clinic with id ${id} is already deactivated`);
            }
            clinic.isActive = false;
            await queryRunner.manager.save(clinic);
            const clinicUsers = await queryRunner.manager.find(clinic_user_entity_1.ClinicUser, {
                where: { clinicId: id }
            });
            if (clinicUsers.length > 0) {
                const userIds = clinicUsers.map(clinicUser => clinicUser.userId);
                await queryRunner.manager.update(user_entity_1.User, { id: (0, typeorm_2.In)(userIds) }, { isActive: false });
            }
            await queryRunner.commitTransaction();
            return clinic;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error('Error deactivating clinic', { error });
            if (error instanceof common_2.NotFoundException ||
                error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('An error occurred during clinic deactivation');
        }
        finally {
            await queryRunner.release();
        }
    }
    async reactivateClinic(id) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const clinic = await queryRunner.manager.findOne(clinic_entity_1.ClinicEntity, {
                where: { id, deletedAt: (0, typeorm_2.IsNull)() }
            });
            if (!clinic) {
                throw new common_2.NotFoundException(`Clinic with id ${id} not found`);
            }
            if (clinic.isActive) {
                throw new common_1.ConflictException(`Clinic with id ${id} is already active`);
            }
            clinic.isActive = true;
            await queryRunner.manager.save(clinic);
            const clinicUsers = await queryRunner.manager.find(clinic_user_entity_1.ClinicUser, {
                where: { clinicId: id }
            });
            if (clinicUsers.length > 0) {
                const userIds = clinicUsers.map(clinicUser => clinicUser.userId);
                await queryRunner.manager.update(user_entity_1.User, { id: (0, typeorm_2.In)(userIds) }, { isActive: true });
            }
            await queryRunner.commitTransaction();
            return clinic;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error('Error reactivating clinic', { error });
            if (error instanceof common_2.NotFoundException ||
                error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('An error occurred during clinic reactivation');
        }
        finally {
            await queryRunner.release();
        }
    }
    async softDeleteClinic(id) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const clinic = await queryRunner.manager.findOne(clinic_entity_1.ClinicEntity, {
                where: { id, deletedAt: (0, typeorm_2.IsNull)() }
            });
            if (!clinic) {
                throw new common_2.NotFoundException(`Clinic with id ${id} not found`);
            }
            if (clinic.deletedAt) {
                throw new common_1.ConflictException(`Clinic with id ${id} is already deleted`);
            }
            clinic.deletedAt = new Date();
            clinic.isActive = false; // Also deactivate when soft deleting
            await queryRunner.manager.save(clinic);
            // Also deactivate all users associated with this clinic
            const clinicUsers = await queryRunner.manager.find(clinic_user_entity_1.ClinicUser, {
                where: { clinicId: id }
            });
            if (clinicUsers.length > 0) {
                const userIds = clinicUsers.map(clinicUser => clinicUser.userId);
                await queryRunner.manager.update(user_entity_1.User, { id: (0, typeorm_2.In)(userIds) }, { isActive: false });
            }
            await queryRunner.commitTransaction();
            return clinic;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error('Error soft deleting clinic', { error });
            if (error instanceof common_2.NotFoundException ||
                error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('An error occurred during clinic soft deletion');
        }
        finally {
            await queryRunner.release();
        }
    }
    async processBulkUpload(file, clinicId, brandId) {
        const sheetData = await read_service_1.ReadService.readExcelBuffer(file.buffer);
        const results = {};
        for (const [sheetName, data] of Object.entries(sheetData)) {
            this.logger.log(`Processing sheet: ${sheetName}`);
            try {
                switch (sheetName.toLowerCase()) {
                    case 'consumables':
                        results[sheetName] = await this.processConsumables(data, clinicId, brandId);
                        break;
                    case 'medications':
                        results[sheetName] = await this.processMedications(data, clinicId, brandId);
                        break;
                    case 'products':
                        results[sheetName] = await this.processProducts(data, clinicId, brandId);
                        break;
                    case 'services':
                        results[sheetName] = await this.processServices(data, clinicId, brandId);
                        break;
                    case 'vaccinations':
                        results[sheetName] = await this.processVaccinations(data, clinicId, brandId);
                        break;
                    case 'diagnostics':
                        results[sheetName] = await this.processDiagnostics(data, clinicId, brandId);
                        break;
                    default:
                        this.logger.error(`Unknown sheet type: ${sheetName}`);
                        continue;
                }
            }
            catch (error) {
                this.logger.error(`Error processing ${sheetName}`, error);
                results[sheetName] = {
                    summary: { created: 0, updated: 0, failed: 0 },
                    errors: [],
                    errorsUpdate: [
                        {
                            id: 'bulk-operation',
                            name: sheetName,
                            errors: [`Failed to process sheet: ${sheetName}`]
                        }
                    ]
                };
            }
        }
        return results;
    }
    async processMedications(data, clinicId, brandId) {
        const formattedData = format_service_1.FormatService.formatClinicMedications(data, clinicId, brandId);
        return await this.processItems(formattedData, this.clinicMedicationsService);
    }
    async processConsumables(data, clinicId, brandId) {
        const formattedData = format_service_1.FormatService.formatClinicConsumables(data, clinicId, brandId);
        return await this.processItems(formattedData, this.consumblesService);
    }
    async processVaccinations(data, clinicId, brandId) {
        const formattedData = format_service_1.FormatService.formatClinicVaccinations(data, clinicId, brandId);
        return await this.processItems(formattedData, this.vaccinationService);
    }
    async processServices(data, clinicId, brandId) {
        const formattedData = format_service_1.FormatService.formatClinicServices(data, clinicId, brandId);
        return await this.processItems(formattedData, this.clinicServices);
    }
    async processProducts(data, clinicId, brandId) {
        const formattedData = format_service_1.FormatService.formatClinicProducts(data, clinicId, brandId);
        return await this.processItems(formattedData, this.productsService);
    }
    async processDiagnostics(data, clinicId, brandId) {
        const formattedData = format_service_1.FormatService.formatClinicLabReports(data, clinicId, brandId);
        return await this.processItems(formattedData, this.clinicLabReportService);
    }
    async processItems(formattedData, service) {
        const summary = { updated: 0, created: 0, failed: 0 };
        const errorArray = [];
        const itemsToInsert = [];
        const itemsToUpdate = [];
        for (const item of formattedData.insertArray) {
            try {
                let searchCriteria;
                if (service === this.clinicLabReportService) {
                    searchCriteria = {
                        name: item.name,
                        clinicId: item.clinicId
                    };
                }
                else if (service === this.consumblesService) {
                    searchCriteria = {
                        productName: item.productName,
                        clinicId: item.clinicId
                    };
                }
                else if (service === this.clinicServices) {
                    searchCriteria = {
                        serviceName: item.serviceName,
                        clinicId: item.clinicId
                    };
                }
                else if (service === this.productsService) {
                    searchCriteria = {
                        productName: item.productName,
                        clinicId: item.clinicId
                    };
                }
                else if (service === this.vaccinationService) {
                    searchCriteria = {
                        productName: item.productName,
                        clinicId: item.clinicId
                    };
                }
                else if (service === this.clinicMedicationsService) {
                    searchCriteria = {
                        name: item.name || item.productName,
                        clinicId: item.clinicId
                    };
                }
                this.logger.log('Searching for existing item with criteria:', searchCriteria);
                const existingItem = await service.findOneEntry(searchCriteria);
                this.logger.log('Search result:', existingItem);
                if (existingItem) {
                    this.logger.log('Found existing item, will update:', existingItem);
                    itemsToUpdate.push({ ...existingItem, ...item });
                    summary.updated++;
                }
                else {
                    this.logger.log('No existing item found, will create new');
                    itemsToInsert.push(item);
                    summary.created++;
                }
            }
            catch (error) {
                this.logger.error('Error processing item', { error, item });
                summary.failed++;
                errorArray.push({
                    id: item.uniqueId ||
                        item.name ||
                        item.productName,
                    errors: [
                        `Failed to process: ${item.uniqueId || item.name || item.productName}`
                    ]
                });
            }
        }
        try {
            if (itemsToInsert.length > 0) {
                this.logger.log('Inserting new items', {
                    count: itemsToInsert.length,
                    items: itemsToInsert
                });
                await service.bulkInsert(itemsToInsert);
            }
            if (itemsToUpdate.length > 0) {
                this.logger.log('Updating existing items', {
                    count: itemsToUpdate.length,
                    items: itemsToUpdate
                });
                await service.bulkInsert(itemsToUpdate);
            }
        }
        catch (error) {
            this.logger.error('Error during bulk operation', { error });
            summary.failed += itemsToInsert.length + itemsToUpdate.length;
            summary.created = 0;
            summary.updated = 0;
            errorArray.push({
                id: 'bulk-operation',
                name: 'operation',
                errors: ['Failed to perform bulk operation']
            });
        }
        return {
            summary,
            errors: formattedData.errorArray,
            errorsUpdate: errorArray
        };
    }
    async generateInventoryExcel(clinicId) {
        const clinic = await this.clinicRepository.findOne({
            where: { id: clinicId }
        });
        if (!clinic) {
            throw new common_2.NotFoundException(`Clinic with ID "${clinicId}" not found`);
        }
        const workbook = XLSX.utils.book_new();
        const createSheet = (data, headers, sheetName) => {
            const worksheet = XLSX.utils.aoa_to_sheet([]);
            XLSX.utils.sheet_add_aoa(worksheet, [headers], { origin: 'B2' });
            XLSX.utils.sheet_add_json(worksheet, data, {
                origin: 'B3',
                skipHeader: true
            });
            if (worksheet['!ref']) {
                const headerRange = XLSX.utils.decode_range(worksheet['!ref']);
                for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
                    const cellRef = XLSX.utils.encode_cell({ r: 1, c: col });
                    if (worksheet[cellRef]) {
                        worksheet[cellRef].s = { font: { bold: true } };
                    }
                }
            }
            const columnWidth = 20;
            worksheet['!cols'] = headers.map(() => ({ wch: columnWidth }));
            XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
        };
        const consumables = await this.consumblesService.getConsumables(clinicId);
        const consumablesHeaders = Object.values(inventory_headers_enum_1.ConsumableHeaders);
        const consumablesData = consumables.map(c => ({
            // [ConsumableHeaders.UNIQUE_ID]: c.uniqueId,
            [inventory_headers_enum_1.ConsumableHeaders.PRODUCT_NAME]: c.productName,
            [inventory_headers_enum_1.ConsumableHeaders.CURRENT_STOCK]: c.currentStock,
            [inventory_headers_enum_1.ConsumableHeaders.MINIMUM_QUANTITY]: c.minimumQuantity
        }));
        console.log(consumablesData, consumablesHeaders);
        createSheet(consumablesData, consumablesHeaders, 'Consumables');
        // Products
        const products = await this.productsService.getProducts(clinicId);
        const productsHeaders = Object.values(inventory_headers_enum_1.ProductHeaders);
        const productsData = products.map(p => ({
            // [ProductHeaders.UNIQUE_ID]: p.uniqueId,
            [inventory_headers_enum_1.ProductHeaders.PRODUCT_NAME]: p.productName,
            [inventory_headers_enum_1.ProductHeaders.CHARGEABLE_PRICE]: p.chargeablePrice,
            [inventory_headers_enum_1.ProductHeaders.TAX]: p.tax,
            [inventory_headers_enum_1.ProductHeaders.CURRENT_STOCK]: p.currentStock,
            [inventory_headers_enum_1.ProductHeaders.MINIMUM_QUANTITY]: p.minimumQuantity
        }));
        createSheet(productsData, productsHeaders, 'Products');
        // Services
        const services = await this.clinicServices.getServices(clinicId);
        const servicesHeaders = Object.values(inventory_headers_enum_1.ServiceHeaders);
        const servicesData = services.map(s => ({
            // [ServiceHeaders.UNIQUE_ID]: s.uniqueId,
            [inventory_headers_enum_1.ServiceHeaders.SERVICE_NAME]: s.serviceName,
            [inventory_headers_enum_1.ServiceHeaders.CHARGEABLE_PRICE]: s.chargeablePrice,
            [inventory_headers_enum_1.ServiceHeaders.TAX]: s.tax
        }));
        createSheet(servicesData, servicesHeaders, 'Services');
        // Lab Reports (Diagnostics)
        const labReports = await this.clinicLabReportService.getLabReports(clinicId);
        const filteredLabReports = labReports.filter(l => !l.integrationType);
        const labReportsHeaders = Object.values(inventory_headers_enum_1.DiagnosticHeaders);
        const labReportsData = filteredLabReports.map(l => ({
            [inventory_headers_enum_1.DiagnosticHeaders.SERVICE_NAME]: l.name,
            [inventory_headers_enum_1.DiagnosticHeaders.CHARGEABLE_PRICE]: l.chargeablePrice,
            [inventory_headers_enum_1.DiagnosticHeaders.TAX]: l.tax
        }));
        createSheet(labReportsData, labReportsHeaders, 'Diagnostics');
        // Vaccinations
        const vaccinations = await this.vaccinationService.getVaccinations(clinicId);
        const vaccinationsHeaders = Object.values(inventory_headers_enum_1.VaccinationHeaders);
        const vaccinationsData = vaccinations.map(v => ({
            // [VaccinationHeaders.UNIQUE_ID]: v.uniqueId,
            [inventory_headers_enum_1.VaccinationHeaders.PRODUCT_NAME]: v.productName,
            [inventory_headers_enum_1.VaccinationHeaders.CHARGEABLE_PRICE]: v.chargeablePrice,
            [inventory_headers_enum_1.VaccinationHeaders.TAX]: v.tax,
            [inventory_headers_enum_1.VaccinationHeaders.CURRENT_STOCK]: v.currentStock,
            [inventory_headers_enum_1.VaccinationHeaders.MINIMUM_QUANTITY]: v.minimumQuantity
        }));
        createSheet(vaccinationsData, vaccinationsHeaders, 'Vaccinations');
        // Medications
        const medications = await this.clinicMedicationsService.getMedications(clinicId);
        const medicationsHeaders = Object.values(inventory_headers_enum_1.MedicationHeaders);
        const medicationsData = medications.map(m => ({
            // [MedicationHeaders.UNIQUE_ID]: m.uniqueId,
            [inventory_headers_enum_1.MedicationHeaders.MEDICATION_NAME]: m.name,
            [inventory_headers_enum_1.MedicationHeaders.RESTRICTED_SUBSTANCE]: m.isRestricted,
            [inventory_headers_enum_1.MedicationHeaders.CHARGEABLE_PRICE]: m.chargeablePrice,
            [inventory_headers_enum_1.MedicationHeaders.TAX]: m.tax,
            [inventory_headers_enum_1.MedicationHeaders.CURRENT_STOCK]: m.currentStock,
            [inventory_headers_enum_1.MedicationHeaders.MINIMUM_QUANTITY]: m.minimumQuantity
        }));
        createSheet(medicationsData, medicationsHeaders, 'Medications');
        const excelBuffer = XLSX.write(workbook, {
            bookType: 'xlsx',
            type: 'buffer'
        });
        return excelBuffer;
    }
    async deleteInventoryItem(itemType, itemId) {
        let service;
        switch (itemType) {
            case 'consumables':
                service = this.consumblesService;
                break;
            case 'medications':
                service = this.clinicMedicationsService;
                break;
            case 'products':
                service = this.productsService;
                break;
            case 'services':
                service = this.clinicServices;
                break;
            case 'vaccinations':
                service = this.vaccinationService;
                break;
            case 'diagnostics':
                service = this.clinicLabReportService;
                break;
            default:
                throw new common_2.NotFoundException(`Invalid item type: ${itemType}`);
        }
        try {
            await service.deleteItem(itemId);
            return { message: `${itemType} item deleted successfully` };
        }
        catch (error) {
            if (error instanceof common_2.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Failed to delete ${itemType} item`);
        }
    }
    // Method to retrieve client booking settings, now returns the enhanced DTO
    async getClientBookingSettings(clinicId) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
        this.logger.log('Fetching client booking settings', { clinicId });
        try {
            const clinic = await this.clinicRepository.findOne({
                where: { id: clinicId }
            });
            if (!clinic) {
                this.logger.warn('Clinic not found for settings retrieval', {
                    clinicId
                });
                throw new common_2.NotFoundException(`Clinic with ID "${clinicId}" not found`);
            }
            // Get the settings from the customRule JSONB field
            const settings = (_a = clinic.customRule) === null || _a === void 0 ? void 0 : _a.clientBookingSettings;
            // If settings don't exist or are null, return null
            if (!settings) {
                this.logger.log('Client booking settings not configured for clinic', { clinicId });
                return null;
            }
            // Prepare the base response object conforming to the DTO
            const response = {
                isEnabled: (_b = settings.isEnabled) !== null && _b !== void 0 ? _b : false, // Provide default
                allowAllDoctors: (_c = settings.allowAllDoctors) !== null && _c !== void 0 ? _c : false, // Default to false if null/undefined
                workingHours: (_d = settings.workingHours) !== null && _d !== void 0 ? _d : undefined, // Use undefined if null
                allowedDoctorIds: (_e = settings.allowedDoctorIds) !== null && _e !== void 0 ? _e : undefined,
                // Include new time duration fields
                minBookingLeadTime: (_f = settings.minBookingLeadTime) !== null && _f !== void 0 ? _f : undefined,
                modificationDeadlineTime: (_g = settings.modificationDeadlineTime) !== null && _g !== void 0 ? _g : undefined,
                maxAdvanceBookingTime: (_h = settings.maxAdvanceBookingTime) !== null && _h !== void 0 ? _h : undefined,
                // Legacy fields
                minBookingLeadHours: (_j = settings.minBookingLeadHours) !== null && _j !== void 0 ? _j : undefined,
                modificationDeadlineHours: (_k = settings.modificationDeadlineHours) !== null && _k !== void 0 ? _k : undefined,
                allowedDoctorsInfo: [] // Initialize as empty array for consistency
            };
            // If allowedDoctorIds exist and the array is not empty, fetch doctor names
            if (settings.allowedDoctorIds &&
                settings.allowedDoctorIds.length > 0) {
                this.logger.log('Fetching doctor details for allowed IDs', {
                    clinicId,
                    ids: settings.allowedDoctorIds
                });
                try {
                    const allowedClinicUsers = await this.clinicUserRepository.find({
                        where: {
                            clinicId: clinicId, // Ensure users belong to the correct clinic
                            id: (0, typeorm_2.In)(settings.allowedDoctorIds)
                        },
                        relations: ['user'], // Still need to load the related User entity
                        select: {
                            // Select necessary fields
                            id: true,
                            userId: true,
                            user: {
                                id: true,
                                firstName: true,
                                lastName: true
                            }
                        }
                    });
                    // Log the raw result from the database query
                    this.logger.log('Found clinic users from DB query (using ClinicUser ID):', {
                        clinicId,
                        allowedClinicUsers: JSON.stringify(allowedClinicUsers)
                    });
                    // Map the found clinic users to the DoctorInfo structure
                    // The response still needs the User ID and name
                    response.allowedDoctorsInfo = allowedClinicUsers.map(cu => {
                        var _a, _b, _c, _d;
                        return ({
                            id: cu.userId, // Return the actual User ID
                            // Construct the full name
                            name: `${(_b = (_a = cu.user) === null || _a === void 0 ? void 0 : _a.firstName) !== null && _b !== void 0 ? _b : ''} ${(_d = (_c = cu.user) === null || _c === void 0 ? void 0 : _c.lastName) !== null && _d !== void 0 ? _d : ''}`.trim() ||
                                'Name Unavailable'
                        });
                    });
                    this.logger.log('Successfully fetched doctor details', {
                        clinicId,
                        count: response.allowedDoctorsInfo.length
                    });
                }
                catch (userQueryError) {
                    // Log the error but potentially continue without doctor names if that's acceptable
                    this.logger.error('Error fetching doctor details for client booking settings', {
                        clinicId,
                        allowedDoctorIds: settings.allowedDoctorIds,
                        error: userQueryError
                    });
                    // Decide if you want to throw or return partial data. Here, we'll return without doctor names.
                    response.allowedDoctorsInfo = []; // Or undefined, depending on desired behavior on error
                }
            }
            else {
                this.logger.log('No allowedDoctorIds specified or array is empty', {
                    clinicId
                });
                // Ensure allowedDoctorsInfo is an empty array if no IDs are provided
                // response.allowedDoctorsInfo = []; // Already initialized above
            }
            // Return the populated response object
            return response;
        }
        catch (error) {
            this.logger.error('Error fetching client booking settings', {
                clinicId,
                error
            });
            // Re-throw known exceptions
            if (error instanceof common_2.NotFoundException ||
                error instanceof common_1.InternalServerErrorException) {
                throw error;
            }
            // Throw a generic server error for other unexpected issues
            throw new common_1.InternalServerErrorException('An unexpected error occurred while retrieving client booking settings');
        }
    }
    // Method to update client booking settings
    async updateClientBookingSettings(clinicId, dto, updatedBy) {
        this.logger.log('Updating client booking settings', {
            clinicId,
            dto,
            updatedBy
        });
        try {
            const clinic = await this.clinicRepository.findOne({
                where: { id: clinicId }
            });
            if (!clinic) {
                this.logger.warn('Clinic not found for settings update', {
                    clinicId
                });
                throw new common_2.NotFoundException(`Clinic with ID "${clinicId}" not found`);
            }
            // Get existing customRule or initialize with default structure if null/undefined
            const currentCustomRule = clinic.customRule || {
                patientLastNameAsOwnerLastName: false, // Keep existing or default value
                clientBookingSettings: null
            };
            // Get existing settings or initialize an empty object if null/undefined
            // Provide a default structure conforming to ClientBookingSettings if null
            const currentSettings = currentCustomRule.clientBookingSettings || {
                isEnabled: false,
                allowAllDoctors: false, // Default for new field
                workingHours: null,
                allowedDoctorIds: null,
                minBookingLeadTime: null,
                modificationDeadlineTime: null,
                maxAdvanceBookingTime: null,
                minBookingLeadHours: null,
                modificationDeadlineHours: null
            };
            // Merge the DTO into the current settings
            // Ensure the result conforms to ClientBookingSettings
            const mergedSettings = {
                isEnabled: dto.isEnabled !== undefined
                    ? dto.isEnabled
                    : currentSettings.isEnabled,
                allowAllDoctors: dto.allowAllDoctors !== undefined
                    ? dto.allowAllDoctors
                    : currentSettings.allowAllDoctors,
                workingHours: dto.workingHours !== undefined
                    ? dto.workingHours
                    : currentSettings.workingHours,
                allowedDoctorIds: dto.allowedDoctorIds !== undefined
                    ? dto.allowedDoctorIds
                    : currentSettings.allowedDoctorIds,
                // New time duration fields
                minBookingLeadTime: dto.minBookingLeadTime !== undefined
                    ? dto.minBookingLeadTime
                    : currentSettings.minBookingLeadTime,
                modificationDeadlineTime: dto.modificationDeadlineTime !== undefined
                    ? dto.modificationDeadlineTime
                    : currentSettings.modificationDeadlineTime,
                maxAdvanceBookingTime: dto.maxAdvanceBookingTime !== undefined
                    ? dto.maxAdvanceBookingTime
                    : currentSettings.maxAdvanceBookingTime,
                // Legacy fields for backward compatibility
                minBookingLeadHours: dto.minBookingLeadHours !== undefined
                    ? dto.minBookingLeadHours
                    : currentSettings.minBookingLeadHours,
                modificationDeadlineHours: dto.modificationDeadlineHours !== undefined
                    ? dto.modificationDeadlineHours
                    : currentSettings.modificationDeadlineHours
            };
            // If allowAllDoctors is true, force allowedDoctorIds to null
            if (mergedSettings.allowAllDoctors === true) {
                mergedSettings.allowedDoctorIds = null;
                this.logger.log('allowAllDoctors is true, setting allowedDoctorIds to null', { clinicId });
            }
            // Update the customRule object
            currentCustomRule.clientBookingSettings = mergedSettings;
            // Assign the updated rule back to the clinic entity
            clinic.customRule = currentCustomRule;
            clinic.updatedBy = updatedBy; // Set who updated the record
            // Save the updated clinic entity
            const updatedClinic = await this.clinicRepository.save(clinic);
            this.logger.log('Client booking settings updated successfully', {
                clinicId
            });
            return updatedClinic;
        }
        catch (error) {
            this.logger.error('Error updating client booking settings', {
                clinicId,
                dto,
                error
            });
            if (error instanceof common_2.NotFoundException) {
                throw error;
            }
            // Consider specific error handling for JSON serialization if needed, though TypeORM handles JSONB well
            throw new common_1.InternalServerErrorException('Failed to update client booking settings');
        }
    }
    // Method to get clinic settings
    async getClinicSettings(clinicId) {
        var _a, _b, _c;
        this.logger.log('Fetching clinic settings', { clinicId });
        try {
            const clinic = await this.clinicRepository.findOne({
                where: { id: clinicId }
            });
            if (!clinic) {
                this.logger.warn('Clinic not found for settings retrieval', {
                    clinicId
                });
                throw new common_2.NotFoundException(`Clinic with ID "${clinicId}" not found`);
            }
            // Get the settings from the customRule JSONB field
            const customRule = clinic.customRule || {};
            // Return settings with defaults if not set
            return {
                patientLastNameAsOwnerLastName: (_a = customRule.patientLastNameAsOwnerLastName) !== null && _a !== void 0 ? _a : false,
                defaultPatientList: (_b = customRule.defaultPatientList) !== null && _b !== void 0 ? _b : 'all',
                appointmentBookingList: (_c = customRule.appointmentBookingList) !== null && _c !== void 0 ? _c : 'alive'
            };
        }
        catch (error) {
            this.logger.error('Error fetching clinic settings', {
                clinicId,
                error
            });
            if (error instanceof common_2.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to fetch clinic settings');
        }
    }
    // Method to update clinic settings
    async updateClinicSettings(clinicId, settingsDto, updatedBy) {
        var _a, _b, _c;
        this.logger.log('Updating clinic settings', {
            clinicId,
            settingsDto,
            updatedBy
        });
        try {
            const clinic = await this.clinicRepository.findOne({
                where: { id: clinicId }
            });
            if (!clinic) {
                this.logger.warn('Clinic not found for settings update', {
                    clinicId
                });
                throw new common_2.NotFoundException(`Clinic with ID "${clinicId}" not found`);
            }
            // Get existing customRule or initialize with default structure
            const currentCustomRule = clinic.customRule || {
                patientLastNameAsOwnerLastName: false,
                defaultPatientList: 'all',
                appointmentBookingList: 'alive'
            };
            // Update only the provided settings
            if (settingsDto.patientLastNameAsOwnerLastName !== undefined) {
                currentCustomRule.patientLastNameAsOwnerLastName =
                    settingsDto.patientLastNameAsOwnerLastName;
            }
            if (settingsDto.defaultPatientList !== undefined) {
                currentCustomRule.defaultPatientList =
                    settingsDto.defaultPatientList;
            }
            if (settingsDto.appointmentBookingList !== undefined) {
                currentCustomRule.appointmentBookingList =
                    settingsDto.appointmentBookingList;
            }
            // Update the customRule object
            clinic.customRule = currentCustomRule;
            clinic.updatedBy = updatedBy;
            // Save the updated clinic entity
            await this.clinicRepository.save(clinic);
            this.logger.log('Clinic settings updated successfully', {
                clinicId
            });
            // Return the updated settings with defaults
            return {
                patientLastNameAsOwnerLastName: (_a = currentCustomRule.patientLastNameAsOwnerLastName) !== null && _a !== void 0 ? _a : false,
                defaultPatientList: (_b = currentCustomRule.defaultPatientList) !== null && _b !== void 0 ? _b : 'all',
                appointmentBookingList: (_c = currentCustomRule.appointmentBookingList) !== null && _c !== void 0 ? _c : 'alive'
            };
        }
        catch (error) {
            this.logger.error('Error updating clinic settings', {
                clinicId,
                settingsDto,
                error
            });
            if (error instanceof common_2.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to update clinic settings');
        }
    }
};
exports.ClinicService = ClinicService;
exports.ClinicService = ClinicService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(clinic_entity_1.ClinicEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(clinic_room_entity_1.ClinicRoomEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(clinic_user_entity_1.ClinicUser)),
    __param(10, (0, common_1.Inject)((0, common_1.forwardRef)(() => users_service_1.UsersService))),
    __param(13, (0, common_1.Inject)((0, common_1.forwardRef)(() => brands_service_1.BrandService))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        winston_logger_service_1.WinstonLogger,
        clinic_consumbles_service_1.ClinicConsumblesService,
        clinic_medications_service_1.ClinicMedicationsService,
        clinic_products_service_1.ClinicProductsService,
        clinic_services_service_1.ClinicServicesService,
        clinic_vaccinations_service_1.ClinicVaccinationsService,
        clinic_lab_report_service_1.ClinicLabReportService,
        users_service_1.UsersService,
        send_mail_service_1.SESMailService,
        typeorm_2.DataSource,
        brands_service_1.BrandService])
], ClinicService);
//# sourceMappingURL=clinic.service.js.map