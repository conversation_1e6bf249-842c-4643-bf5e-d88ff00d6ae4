"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicPlansController = void 0;
const common_1 = require("@nestjs/common");
const clinic_plans_service_1 = require("./clinic-plans.service");
const enum_plan_type_1 = require("./enums/enum-plan-type");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let ClinicPlansController = class ClinicPlansController {
    constructor(clinicPlanService) {
        this.clinicPlanService = clinicPlanService;
    }
    async getClinicPlans(clinicId, search, exclude) {
        // Parse the exclude parameter into an array of EnumPlanType
        const excludeTypes = exclude
            ? exclude.split(',')
            : [];
        return this.clinicPlanService.findAll(clinicId, search, excludeTypes);
    }
};
exports.ClinicPlansController = ClinicPlansController;
__decorate([
    (0, common_1.Get)(),
    (0, track_method_decorator_1.TrackMethod)('getClinicPlans-clinic-plans'),
    __param(0, (0, common_1.Query)('clinicId')),
    __param(1, (0, common_1.Query)('search')),
    __param(2, (0, common_1.Query)('exclude')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], ClinicPlansController.prototype, "getClinicPlans", null);
exports.ClinicPlansController = ClinicPlansController = __decorate([
    (0, common_1.Controller)('clinic-plans'),
    __metadata("design:paramtypes", [clinic_plans_service_1.ClinicPlansService])
], ClinicPlansController);
//# sourceMappingURL=clinic-plans.controller.js.map