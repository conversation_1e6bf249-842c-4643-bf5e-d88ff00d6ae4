import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../utils/redis/redis.service';

@Injectable()
export class SessionService {
	private readonly logger = new Logger(SessionService.name);
	private static readonly KEY_PREFIX = 'session';
	private static readonly ROUTE_KEY_PREFIX = 'route';

	constructor(private readonly redisService: RedisService) {}

	private getKey(userId: string): string {
		return `${SessionService.KEY_PREFIX}:${userId}`;
	}

	private getRouteKey(userId: string): string {
		return `${SessionService.ROUTE_KEY_PREFIX}:${userId}`;
	}

	/**
	 * Persist the active sessionId for the user with a TTL (seconds).
	 */
	async setUserSession(
		userId: string,
		sessionId: string,
		ttlSeconds: number
	): Promise<void> {
		try {
			await this.redisService
				.getClient()
				.set(this.getKey(userId), sessionId, 'EX', ttlSeconds);
		} catch (error) {
			this.logger.error('Failed to set user session', { userId, error });
			throw error;
		}
	}

	/**
	 * Retrieve the current sessionId stored for the user (if any).
	 */
	async getUserSession(userId: string): Promise<string | null> {
		try {
			return await this.redisService.get(this.getKey(userId));
		} catch (error) {
			this.logger.error('Failed to get user session', { userId, error });
			throw error;
		}
	}

	/**
	 * Clear the stored session for the user (used on logout or account disable).
	 */
	async clearUserSession(userId: string): Promise<void> {
		try {
			await this.redisService.getClient().del(this.getKey(userId));
		} catch (error) {
			this.logger.error('Failed to clear user session', {
				userId,
				error
			});
			throw error;
		}
	}

	/**
	 * Store the user's current route in Redis with a TTL (seconds).
	 */
	async setUserRoute(
		userId: string,
		route: string,
		ttlSeconds: number
	): Promise<void> {
		try {
			await this.redisService
				.getClient()
				.set(this.getRouteKey(userId), route, 'EX', ttlSeconds);
		} catch (error) {
			this.logger.error('Failed to set user route', {
				userId,
				route,
				error
			});
			throw error;
		}
	}

	/**
	 * Retrieve the current route stored for the user (if any).
	 */
	async getUserRoute(userId: string): Promise<string | null> {
		try {
			return await this.redisService.get(this.getRouteKey(userId));
		} catch (error) {
			this.logger.error('Failed to get user route', { userId, error });
			throw error;
		}
	}

	/**
	 * Clear the stored route for the user.
	 */
	async clearUserRoute(userId: string): Promise<void> {
		try {
			await this.redisService.getClient().del(this.getRouteKey(userId));
		} catch (error) {
			this.logger.error('Failed to clear user route', { userId, error });
			throw error;
		}
	}
}
