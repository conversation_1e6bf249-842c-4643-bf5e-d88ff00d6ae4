'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getAuth } from '../../services/identity.service';
import { Role } from '../../types/roles';

export default function Home() {
    const router = useRouter();

    // Check user role and redirect if not super admin
    useEffect(() => {
        const auth = getAuth();
        const userRole = auth?.role;

        if (userRole !== Role.SUPER_ADMIN) {
            router.push('/dashboard');
            return;
        }
    }, [router]);

    return <main className="">Page</main>;
}
