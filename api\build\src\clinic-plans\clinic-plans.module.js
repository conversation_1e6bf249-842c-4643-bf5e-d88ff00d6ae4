"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicPlansModule = void 0;
const common_1 = require("@nestjs/common");
const clinic_plans_service_1 = require("./clinic-plans.service");
const clinic_plans_controller_1 = require("./clinic-plans.controller");
const clinic_plan_entity_1 = require("./entities/clinic-plan.entity");
const typeorm_1 = require("@nestjs/typeorm");
const clinic_product_entity_1 = require("../clinic-products/entities/clinic-product.entity");
const clinic_products_service_1 = require("../clinic-products/clinic-products.service");
const clinic_vaccinations_service_1 = require("../clinic-vaccinations/clinic-vaccinations.service");
const clinic_services_service_1 = require("../clinic-services/clinic-services.service");
const clinic_vaccination_entity_1 = require("../clinic-vaccinations/entities/clinic-vaccination.entity");
const clinic_service_entity_1 = require("../clinic-services/entities/clinic-service.entity");
const clinic_medications_service_1 = require("../clinic-medications/clinic-medications.service");
const clinic_medication_entity_1 = require("../clinic-medications/entities/clinic-medication.entity");
const clinic_lab_report_module_1 = require("../clinic-lab-report/clinic-lab-report.module");
let ClinicPlansModule = class ClinicPlansModule {
};
exports.ClinicPlansModule = ClinicPlansModule;
exports.ClinicPlansModule = ClinicPlansModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                clinic_plan_entity_1.ClinicPlan,
                clinic_product_entity_1.ClinicProductEntity,
                clinic_vaccination_entity_1.ClinicVaccinationEntity,
                clinic_service_entity_1.ClinicServiceEntity,
                clinic_medication_entity_1.ClinicMedicationEntity
            ]),
            clinic_lab_report_module_1.ClinicLabReportModule
        ],
        controllers: [clinic_plans_controller_1.ClinicPlansController],
        providers: [
            clinic_plans_service_1.ClinicPlansService,
            clinic_products_service_1.ClinicProductsService,
            clinic_vaccinations_service_1.ClinicVaccinationsService,
            clinic_services_service_1.ClinicServicesService,
            clinic_medications_service_1.ClinicMedicationsService
        ]
    })
], ClinicPlansModule);
//# sourceMappingURL=clinic-plans.module.js.map