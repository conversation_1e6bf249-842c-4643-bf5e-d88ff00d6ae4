"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicPlansService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const clinic_plan_entity_1 = require("./entities/clinic-plan.entity");
const typeorm_2 = require("typeorm");
const clinic_products_service_1 = require("../clinic-products/clinic-products.service");
const clinic_vaccinations_service_1 = require("../clinic-vaccinations/clinic-vaccinations.service");
const clinic_services_service_1 = require("../clinic-services/clinic-services.service");
const enum_plan_type_1 = require("./enums/enum-plan-type");
const clinic_lab_report_service_1 = require("../clinic-lab-report/clinic-lab-report.service");
const clinic_medications_service_1 = require("../clinic-medications/clinic-medications.service");
let ClinicPlansService = class ClinicPlansService {
    constructor(clinicPlansRepository, clinicProductsservice, clinicVaccinationsservice, clinicServicesservice, clinicMedicationService, clinicLabReportsService) {
        this.clinicPlansRepository = clinicPlansRepository;
        this.clinicProductsservice = clinicProductsservice;
        this.clinicVaccinationsservice = clinicVaccinationsservice;
        this.clinicServicesservice = clinicServicesservice;
        this.clinicMedicationService = clinicMedicationService;
        this.clinicLabReportsService = clinicLabReportsService;
    }
    async findAll(clinicId, searchKeyword, excludeTypes) {
        const [services, vaccinations, medications, products, labreports] = await Promise.all([
            this.clinicServicesservice.getServices(clinicId),
            this.clinicVaccinationsservice.getVaccinations(clinicId),
            this.clinicMedicationService.getMedications(clinicId),
            this.clinicProductsservice.getProducts(clinicId),
            this.clinicLabReportsService.getLabReports(clinicId)
        ]);
        let combinedData = [
            ...products === null || products === void 0 ? void 0 : products.map((item) => ({
                type: enum_plan_type_1.EnumPlanType.Product,
                ...item
            })),
            ...services === null || services === void 0 ? void 0 : services.map((item) => ({
                type: enum_plan_type_1.EnumPlanType.Service,
                ...item
            })),
            ...vaccinations === null || vaccinations === void 0 ? void 0 : vaccinations.map((item) => ({
                type: enum_plan_type_1.EnumPlanType.Vaccination,
                ...item
            })),
            ...medications === null || medications === void 0 ? void 0 : medications.map((item) => ({
                type: enum_plan_type_1.EnumPlanType.Medication,
                ...item
            })),
            ...labreports === null || labreports === void 0 ? void 0 : labreports.map((item) => ({
                type: enum_plan_type_1.EnumPlanType.Labreport,
                ...item
            }))
        ];
        // Filter out the excluded types
        if (excludeTypes && excludeTypes.length > 0) {
            combinedData = combinedData.filter(item => !excludeTypes.includes(item.type));
        }
        return combinedData;
    }
};
exports.ClinicPlansService = ClinicPlansService;
exports.ClinicPlansService = ClinicPlansService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(clinic_plan_entity_1.ClinicPlan)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        clinic_products_service_1.ClinicProductsService,
        clinic_vaccinations_service_1.ClinicVaccinationsService,
        clinic_services_service_1.ClinicServicesService,
        clinic_medications_service_1.ClinicMedicationsService,
        clinic_lab_report_service_1.ClinicLabReportService])
], ClinicPlansService);
//# sourceMappingURL=clinic-plans.service.js.map