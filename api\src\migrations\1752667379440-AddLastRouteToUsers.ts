import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddLastRouteToUsers1752667379440 implements MigrationInterface {
	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.addColumn(
			'users',
			new TableColumn({
				name: 'last_route',
				type: 'varchar',
				length: '500',
				isNullable: true,
				comment:
					'Stores the last visited route path for session memory functionality'
			})
		);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.dropColumn('users', 'last_route');
	}
}
