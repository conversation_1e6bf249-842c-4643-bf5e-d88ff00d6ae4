// components/Layout/ClientLayout.tsx
'use client';

import { useEffect, useState } from 'react';
import FetchedLayout from './components/Layout/FetchedLayout';
import ErrorBoundary from './components/ErrorBoundary';
import SessionEndedModal from './molecules/SessionEndedModal';
import { SessionProvider, useSession } from './context/SessionContext';
import { resetSessionEndedFlag } from './services/http.service';
import RouteTracker from './components/RouteTracker';

// Inner component that uses the session context
function ClientLayoutInner({ children }: { children: React.ReactNode }) {
    const [isMounted, setIsMounted] = useState(false);
    const { sessionEnded, setSessionEnded } = useSession();

    useEffect(() => {
        setIsMounted(true);

        // Add event listener to handle unload
        const handleUnload = () => {
            if (sessionEnded) {
                resetSessionEndedFlag();
            }
        };

        window.addEventListener('beforeunload', handleUnload);

        return () => {
            window.removeEventListener('beforeunload', handleUnload);
        };
    }, [sessionEnded]);

    const closeModal = () => {
        setSessionEnded(false);
        resetSessionEndedFlag();
    };

    // If not mounted yet, render nothing to improve initial loading performance
    if (!isMounted) return null;

    // Render only the SessionEndedModal if the session has ended
    if (sessionEnded) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <SessionEndedModal isOpen={true} onClose={closeModal} />
            </div>
        );
    }

    // Otherwise, render the full application
    return (
        <ErrorBoundary>
            <RouteTracker />
            <FetchedLayout>{children}</FetchedLayout>
        </ErrorBoundary>
    );
}

// Main layout that provides the SessionProvider context
export default function ClientLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <SessionProvider>
            <ClientLayoutInner>{children}</ClientLayoutInner>
        </SessionProvider>
    );
}
